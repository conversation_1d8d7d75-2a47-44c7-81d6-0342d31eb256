<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试永久删除功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">测试永久删除功能</h1>
        
        <div class="alert alert-success">
            <h5>✅ 永久删除功能增强：</h5>
            <ul class="mb-0">
                <li>使用原生SQL强制删除，确保记录从数据库中完全移除</li>
                <li>添加@Transactional注解确保事务一致性</li>
                <li>使用flush()方法强制刷新持久化上下文</li>
                <li>删除后立即验证确保删除成功</li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h3>永久删除测试</h3>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">测试步骤</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="testVideoId" class="form-label">测试视频ID：</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="testVideoId" placeholder="输入视频ID">
                                <button class="btn btn-outline-secondary" onclick="getRandomDisabledVideo()">选择禁用视频</button>
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-info" onclick="checkVideoExists()">
                                <i class="fas fa-search me-1"></i>检查视频是否存在
                            </button>
                            <button class="btn btn-warning" onclick="ensureVideoDisabled()">
                                <i class="fas fa-pause me-1"></i>确保视频已禁用
                            </button>
                            <button class="btn btn-danger" onclick="permanentDeleteVideo()">
                                <i class="fas fa-trash me-1"></i>永久删除视频
                            </button>
                            <button class="btn btn-success" onclick="verifyPermanentDeletion()">
                                <i class="fas fa-check me-1"></i>验证永久删除
                            </button>
                        </div>
                        
                        <hr>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="openAdminPage()">
                                <i class="fas fa-external-link-alt me-1"></i>打开Admin页面
                            </button>
                            <button class="btn btn-secondary" onclick="clearLog()">
                                <i class="fas fa-eraser me-1"></i>清空日志
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>测试日志</h3>
                <div class="card">
                    <div class="card-body p-0">
                        <div id="testLog" style="height: 400px; overflow-y: auto; background-color: #1a1a1a; color: #00ff00; padding: 15px; font-family: 'Courier New', monospace; font-size: 0.9em;">
                            <div>[系统] 永久删除测试工具已加载</div>
                            <div>[提示] 请选择一个禁用状态的视频进行测试</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>永久删除技术实现</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>技术点</th>
                                <th>实现方式</th>
                                <th>作用</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>原生SQL删除</strong></td>
                                <td><code>@Query(value = "DELETE FROM videos WHERE id = :id", nativeQuery = true)</code></td>
                                <td>绕过JPA缓存，直接操作数据库</td>
                            </tr>
                            <tr>
                                <td><strong>事务管理</strong></td>
                                <td><code>@Transactional</code> + <code>@Modifying</code></td>
                                <td>确保删除操作的原子性</td>
                            </tr>
                            <tr>
                                <td><strong>强制刷新</strong></td>
                                <td><code>videoRepository.flush()</code></td>
                                <td>立即将更改同步到数据库</td>
                            </tr>
                            <tr>
                                <td><strong>删除验证</strong></td>
                                <td><code>videoRepository.findById(id)</code></td>
                                <td>确认记录已从数据库中移除</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🔧 核心代码实现</h5>
                    </div>
                    <div class="card-body">
                        <h6>Repository层 - 原生SQL删除：</h6>
                        <pre class="bg-dark text-light p-3"><code>@Modifying
@Query(value = "DELETE FROM videos WHERE id = :id", nativeQuery = true)
int deleteVideoById(@Param("id") Long id);</code></pre>
                        
                        <h6>Service层 - 强制删除方法：</h6>
                        <pre class="bg-dark text-light p-3"><code>@Transactional
public boolean forceDeleteVideoById(Long id) {
    // 检查视频状态
    // 删除OSS文件
    // 使用原生SQL强制删除
    int deletedRows = videoRepository.deleteVideoById(id);
    return deletedRows > 0;
}</code></pre>
                        
                        <h6>API层 - 删除接口：</h6>
                        <pre class="bg-dark text-light p-3"><code>@DeleteMapping("/{id}")
public ResponseEntity&lt;ApiResponse&lt;String&gt;&gt; deleteVideo(@PathVariable Long id) {
    boolean deleted = videoService.forceDeleteVideoById(id);
    return ResponseEntity.ok(ApiResponse.success("视频已永久删除"));
}</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': '#00ff00',
                'success': '#00ff00',
                'error': '#ff0000',
                'warning': '#ffff00',
                'system': '#00ffff'
            }[type] || '#00ff00';
            
            logDiv.innerHTML += `<div style="color: ${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('testLog').innerHTML = `
                <div>[系统] 日志已清空</div>
                <div>[提示] 可以重新开始测试</div>
            `;
        }

        function getVideoId() {
            const videoId = document.getElementById('testVideoId').value;
            if (!videoId) {
                log('❌ 请先输入视频ID', 'error');
                return null;
            }
            return videoId;
        }

        async function getRandomDisabledVideo() {
            log('🔍 查找禁用状态的视频...', 'system');
            
            try {
                const response = await fetch('/api/videos?page=0&size=50');
                const result = await response.json();
                
                if (result.success && result.data) {
                    const disabledVideos = result.data.filter(video => !video.isActive);
                    
                    if (disabledVideos.length > 0) {
                        const randomVideo = disabledVideos[Math.floor(Math.random() * disabledVideos.length)];
                        document.getElementById('testVideoId').value = randomVideo.id;
                        log(`✅ 找到禁用视频: ID=${randomVideo.id}, 标题="${randomVideo.title}"`, 'success');
                    } else {
                        log('⚠️ 没有找到禁用状态的视频', 'warning');
                        log('请先在Admin页面禁用一个视频', 'warning');
                    }
                } else {
                    log('❌ 获取视频列表失败', 'error');
                }
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function checkVideoExists() {
            const videoId = getVideoId();
            if (!videoId) return;

            log(`🔍 检查视频 ${videoId} 是否存在...`, 'system');
            
            try {
                const response = await fetch(`/api/videos/${videoId}`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    log(`✅ 视频存在: ${result.data.title}`, 'success');
                    log(`   状态: ${result.data.isActive ? '启用' : '禁用'}`, 'info');
                    log(`   创建时间: ${new Date(result.data.createdTime).toLocaleString()}`, 'info');
                } else {
                    log(`❌ 视频不存在或已被删除`, 'error');
                }
            } catch (error) {
                log(`❌ 检查失败: ${error.message}`, 'error');
            }
        }

        async function ensureVideoDisabled() {
            const videoId = getVideoId();
            if (!videoId) return;

            log(`🔄 确保视频 ${videoId} 处于禁用状态...`, 'system');
            
            try {
                // 先获取视频信息
                const getResponse = await fetch(`/api/videos/${videoId}`);
                const getResult = await getResponse.json();
                
                if (!getResult.success || !getResult.data) {
                    log('❌ 视频不存在', 'error');
                    return;
                }
                
                if (!getResult.data.isActive) {
                    log('✅ 视频已经是禁用状态', 'success');
                    return;
                }
                
                // 禁用视频
                log('正在禁用视频...', 'info');
                const toggleResponse = await fetch(`/api/videos/${videoId}/toggle-status`, {
                    method: 'PUT'
                });
                const toggleResult = await toggleResponse.json();
                
                if (toggleResult.success) {
                    log('✅ 视频已禁用', 'success');
                } else {
                    log(`❌ 禁用失败: ${toggleResult.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 禁用操作失败: ${error.message}`, 'error');
            }
        }

        async function permanentDeleteVideo() {
            const videoId = getVideoId();
            if (!videoId) return;

            log(`🗑️ 开始永久删除视频 ${videoId}...`, 'system');
            log('⚠️ 这是真正的永久删除操作！', 'warning');
            
            try {
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'DELETE'
                });
                
                log(`📡 删除请求状态: ${response.status} ${response.statusText}`, 'info');
                
                const result = await response.json();
                log(`📄 删除响应: ${JSON.stringify(result, null, 2)}`, 'info');
                
                if (result.success) {
                    log(`✅ 删除请求成功: ${result.message}`, 'success');
                    log('🔍 请点击"验证永久删除"确认结果', 'system');
                } else {
                    log(`❌ 删除失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 删除操作失败: ${error.message}`, 'error');
            }
        }

        async function verifyPermanentDeletion() {
            const videoId = getVideoId();
            if (!videoId) return;

            log(`🔍 验证视频 ${videoId} 是否已永久删除...`, 'system');
            
            // 等待一下确保数据库操作完成
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            try {
                const response = await fetch(`/api/videos/${videoId}`);
                const result = await response.json();
                
                log(`📡 验证请求状态: ${response.status} ${response.statusText}`, 'info');
                log(`📄 验证响应: ${JSON.stringify(result, null, 2)}`, 'info');
                
                if (response.status === 404 || !result.success) {
                    log(`🎉 验证成功: 视频已从数据库中永久删除！`, 'success');
                    log(`✅ 永久删除功能工作正常`, 'success');
                } else if (result.success && result.data) {
                    log(`❌ 验证失败: 视频仍然存在于数据库中`, 'error');
                    log(`   标题: ${result.data.title}`, 'error');
                    log(`   状态: ${result.data.isActive ? '启用' : '禁用'}`, 'error');
                    log(`🐛 永久删除功能可能存在问题`, 'error');
                }
            } catch (error) {
                log(`❌ 验证过程出错: ${error.message}`, 'error');
            }
        }

        function openAdminPage() {
            log('🌐 打开Admin页面...', 'system');
            window.open('/admin', '_blank');
        }

        // 页面加载时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 永久删除测试工具已完全加载', 'success');
            log('📝 测试说明:', 'system');
            log('1. 点击"选择禁用视频"自动选择一个可删除的视频', 'info');
            log('2. 按顺序执行各个测试步骤', 'info');
            log('3. 最后验证视频是否真正从数据库中删除', 'info');
        });
    </script>
</body>
</html>
