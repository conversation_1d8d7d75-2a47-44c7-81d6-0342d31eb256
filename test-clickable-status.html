<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试可点击状态切换功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .clickable-status {
            transition: all 0.2s ease;
            user-select: none;
            cursor: pointer;
        }
        
        .clickable-status:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .clickable-status:active {
            transform: scale(0.95);
        }
        
        .demo-status {
            margin: 10px;
            display: inline-block;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">测试可点击状态切换功能</h1>
        
        <div class="alert alert-success">
            <h5>✅ 新的状态切换功能：</h5>
            <ul class="mb-0">
                <li>直接点击状态标识即可切换启用/禁用状态</li>
                <li>移除了单独的状态切换按钮，界面更简洁</li>
                <li>状态切换后立即更新显示，无需刷新页面</li>
                <li>删除按钮根据状态自动显示/隐藏</li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h3>功能演示</h3>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">状态标识演示</h5>
                    </div>
                    <div class="card-body">
                        <p>以下是可点击的状态标识示例：</p>
                        
                        <div class="demo-status">
                            <span class="status-badge badge bg-success clickable-status" 
                                  onclick="demoToggle(this, true)"
                                  title="点击切换为禁用状态">
                                <i class="fas fa-check me-1"></i>启用
                            </span>
                        </div>
                        
                        <div class="demo-status">
                            <span class="status-badge badge bg-secondary clickable-status" 
                                  onclick="demoToggle(this, false)"
                                  title="点击切换为启用状态">
                                <i class="fas fa-pause me-1"></i>禁用
                            </span>
                        </div>
                        
                        <p class="mt-3 text-muted">
                            <small>点击上面的状态标识查看切换效果</small>
                        </p>
                        
                        <div class="alert alert-info mt-3">
                            <h6><i class="fas fa-lightbulb me-2"></i>交互说明：</h6>
                            <ul class="mb-0">
                                <li>鼠标悬停时状态标识会放大并显示阴影</li>
                                <li>点击时会有缩放动画效果</li>
                                <li>工具提示显示点击后的状态变化</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>测试验证</h3>
                
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="testAdminPage()">
                        <i class="fas fa-external-link-alt me-1"></i>打开Admin页面测试
                    </button>
                    <button class="btn btn-secondary" onclick="showInstructions()">
                        <i class="fas fa-question-circle me-1"></i>查看测试说明
                    </button>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">测试日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" style="height: 250px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <p class="text-muted">点击测试按钮查看结果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>功能对比</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>功能</th>
                                <th>修改前</th>
                                <th>修改后</th>
                                <th>优势</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>状态切换</strong></td>
                                <td>需要点击单独的切换按钮</td>
                                <td>直接点击状态标识</td>
                                <td>操作更直观，减少点击步骤</td>
                            </tr>
                            <tr>
                                <td><strong>界面布局</strong></td>
                                <td>操作列有3个按钮</td>
                                <td>操作列只有2个按钮</td>
                                <td>界面更简洁，空间利用更好</td>
                            </tr>
                            <tr>
                                <td><strong>用户体验</strong></td>
                                <td>需要理解按钮图标含义</td>
                                <td>状态和操作合二为一</td>
                                <td>更符合用户直觉</td>
                            </tr>
                            <tr>
                                <td><strong>视觉反馈</strong></td>
                                <td>页面刷新后看到变化</td>
                                <td>立即更新显示</td>
                                <td>响应更快，体验更流畅</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🔧 实现说明</h5>
                    </div>
                    <div class="card-body">
                        <h6>前端实现：</h6>
                        <ul>
                            <li><strong>HTML结构</strong>：状态标识添加 <code>onclick</code> 事件和 <code>cursor: pointer</code> 样式</li>
                            <li><strong>CSS动画</strong>：悬停放大、点击缩放效果，增强交互体验</li>
                            <li><strong>动态更新</strong>：JavaScript实时更新状态显示，无需页面刷新</li>
                            <li><strong>删除按钮</strong>：根据状态自动显示/隐藏删除按钮</li>
                        </ul>
                        
                        <h6>交互逻辑：</h6>
                        <ul>
                            <li><strong>状态切换</strong>：点击状态标识 → 调用API → 更新显示</li>
                            <li><strong>视觉反馈</strong>：立即更新状态标识的颜色、图标和文字</li>
                            <li><strong>行样式</strong>：同步更新视频行的启用/禁用样式</li>
                            <li><strong>按钮管理</strong>：智能显示/隐藏删除按钮</li>
                        </ul>
                        
                        <h6>用户体验优化：</h6>
                        <ul>
                            <li><strong>直观操作</strong>：点击状态即可切换，符合用户直觉</li>
                            <li><strong>即时反馈</strong>：无需等待页面刷新即可看到变化</li>
                            <li><strong>视觉提示</strong>：工具提示明确说明点击后的效果</li>
                            <li><strong>动画效果</strong>：平滑的过渡动画提升操作体验</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function demoToggle(element, currentStatus) {
            const newStatus = !currentStatus;
            
            log(`演示状态切换: ${currentStatus ? '启用' : '禁用'} → ${newStatus ? '启用' : '禁用'}`, 'info');
            
            // 模拟状态切换效果
            if (newStatus) {
                element.className = 'status-badge badge bg-success clickable-status';
                element.innerHTML = '<i class="fas fa-check me-1"></i>启用';
                element.title = '点击切换为禁用状态';
                element.onclick = () => demoToggle(element, true);
            } else {
                element.className = 'status-badge badge bg-secondary clickable-status';
                element.innerHTML = '<i class="fas fa-pause me-1"></i>禁用';
                element.title = '点击切换为启用状态';
                element.onclick = () => demoToggle(element, false);
            }
            
            log('✅ 状态切换完成', 'success');
        }

        function testAdminPage() {
            log('打开Admin页面进行实际测试...', 'info');
            log('请按以下步骤测试：', 'info');
            log('1. 找到任意视频的状态标识', 'info');
            log('2. 直接点击状态标识（启用/禁用）', 'info');
            log('3. 观察状态是否立即切换', 'info');
            log('4. 检查删除按钮是否正确显示/隐藏', 'info');
            window.open('/admin', '_blank');
        }

        function showInstructions() {
            log('=== 测试说明 ===', 'warning');
            log('1. 状态标识现在可以直接点击', 'info');
            log('2. 点击后状态会立即切换', 'info');
            log('3. 删除按钮只在禁用状态显示', 'info');
            log('4. 无需页面刷新即可看到变化', 'info');
            log('5. 鼠标悬停有放大效果', 'info');
            log('=== 测试完成 ===', 'warning');
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            log('可点击状态切换功能测试页面加载完成', 'info');
            log('现在可以直接点击状态标识来切换启用/禁用状态', 'success');
            log('试试点击上面的演示状态标识', 'info');
        });
    </script>
</body>
</html>
