<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>page-link颜色统一测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        /* 复制项目中的统一分页样式 */
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        /* 激活状态样式 */
        .pagination .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        /* 禁用状态样式 - 统一颜色为#667eea */
        .pagination .page-item.disabled .page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #667eea !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        /* disabled状态的span元素样式 */
        .pagination .page-item.disabled span.page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #667eea !important;
            cursor: not-allowed !important;
        }

        /* 悬停效果 */
        .pagination .page-link:hover:not(.disabled) {
            background: #f8f9fa;
            color: #495057;
            transform: translateY(-1px);
        }
        
        .color-demo {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 3px;
            margin-right: 8px;
            vertical-align: middle;
            border: 1px solid #ddd;
        }
        
        .color-667eea {
            background-color: #667eea;
        }
        
        .status-table {
            width: 100%;
            margin: 20px 0;
        }
        
        .status-table th,
        .status-table td {
            padding: 12px;
            text-align: center;
            border: 1px solid #dee2e6;
        }
        
        .status-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .highlight-color {
            background: #fff3cd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">page-link颜色统一测试</h1>
        
        <!-- 颜色统一说明 -->
        <div class="test-section">
            <h3 class="test-title">
                <span class="color-demo color-667eea"></span>
                颜色统一为 #667eea
            </h3>
            <p>所有page-link元素的颜色现在都统一使用 <code>#667eea</code> 色值，包括：</p>
            <ul>
                <li>正常状态的分页链接</li>
                <li>禁用状态的分页链接</li>
                <li>所有页面的分页组件</li>
            </ul>
        </div>
        
        <!-- 状态对比表格 -->
        <div class="test-section">
            <h3 class="test-title">分页状态对比表</h3>
            <table class="status-table">
                <thead>
                    <tr>
                        <th>状态</th>
                        <th>背景色</th>
                        <th>边框色</th>
                        <th>文字色</th>
                        <th>鼠标样式</th>
                        <th>交互性</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>正常状态</strong></td>
                        <td>透明/默认</td>
                        <td>无</td>
                        <td class="highlight-color">#667eea</td>
                        <td>pointer</td>
                        <td>可点击</td>
                    </tr>
                    <tr>
                        <td><strong>激活状态</strong></td>
                        <td class="highlight-color">#667eea</td>
                        <td class="highlight-color">#667eea</td>
                        <td>white</td>
                        <td>default</td>
                        <td>当前页</td>
                    </tr>
                    <tr>
                        <td><strong>禁用状态</strong></td>
                        <td>#ffffff</td>
                        <td>#dee2e6</td>
                        <td class="highlight-color">#667eea</td>
                        <td>not-allowed</td>
                        <td>不可点击</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 实际效果展示 -->
        <div class="test-section">
            <h3 class="test-title">实际效果展示</h3>
            
            <!-- 第一页状态 -->
            <div class="mb-4">
                <h5>第一页状态（上一页禁用）</h5>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;">2</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            
            <!-- 中间页状态 -->
            <div class="mb-4">
                <h5>中间页状态（所有按钮正常）</h5>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;">1</a>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">2</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;">3</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
            </div>
            
            <!-- 最后一页状态 -->
            <div class="mb-4">
                <h5>最后一页状态（下一页禁用）</h5>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;">1</a>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;">2</a>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">3</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
        
        <!-- 颜色一致性验证 -->
        <div class="test-section">
            <h3 class="test-title">颜色一致性验证</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>正常状态 page-link</h5>
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;">正常链接</a>
                        </li>
                    </ul>
                    <small class="text-muted">颜色: #667eea</small>
                </div>
                <div class="col-md-6">
                    <h5>禁用状态 page-link</h5>
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link">禁用链接</span>
                        </li>
                    </ul>
                    <small class="text-muted">颜色: #667eea（与正常状态一致）</small>
                </div>
            </div>
        </div>
        
        <!-- CSS代码展示 -->
        <div class="test-section">
            <h3 class="test-title">CSS代码</h3>
            <p>以下是统一颜色的CSS代码：</p>
            
            <pre style="background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto;"><code>/* 正常状态 */
.pagination .page-link {
    color: #667eea;
}

/* 禁用状态 - 颜色统一为#667eea */
.pagination .page-item.disabled .page-link {
    color: #667eea !important;
}

.pagination .page-item.disabled span.page-link {
    color: #667eea !important;
}</code></pre>
            
            <div class="alert alert-success mt-3">
                <h5><i class="fas fa-check-circle"></i> 修改完成</h5>
                <p class="mb-0">
                    ✅ 已在 <code>style.css</code> 中统一disabled状态颜色为 #667eea<br>
                    ✅ 已在 <code>videos-style.css</code> 中统一disabled状态颜色为 #667eea<br>
                    ✅ 确保所有page-link元素都使用相同的 #667eea 颜色
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
