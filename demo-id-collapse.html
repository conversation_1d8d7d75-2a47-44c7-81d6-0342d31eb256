<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频ID折叠功能演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* ID折叠样式 */
        .id-container {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .id-collapsed {
            max-width: 50px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .id-expanded {
            max-width: none;
            word-break: break-all;
        }

        .id-toggle-icon {
            font-size: 0.8em;
            margin-left: 4px;
            transition: transform 0.3s ease;
        }

        .id-expanded .id-toggle-icon {
            transform: rotate(180deg);
        }

        .id-container:hover {
            background-color: #e9ecef !important;
            transform: scale(1.02);
        }

        .id-full-text {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }

        .demo-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🆔 视频ID折叠功能演示</h1>
                <p class="text-center text-muted">演示admin页面中的ID折叠展开功能</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card feature-card">
                    <div class="card-body text-center">
                        <h5 class="mb-3">✨ 功能特点</h5>
                        <div class="row">
                            <div class="col-md-4">
                                <i class="fas fa-compress-alt fa-2x mb-2"></i>
                                <p class="mb-0">默认折叠显示</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-mouse-pointer fa-2x mb-2"></i>
                                <p class="mb-0">单击展开/折叠</p>
                            </div>
                            <div class="col-md-4">
                                <i class="fas fa-copy fa-2x mb-2"></i>
                                <p class="mb-0">双击快速复制</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card demo-table">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">📋 视频管理列表演示</h5>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-light btn-sm" onclick="expandAllIds()" title="展开所有ID">
                                    <i class="fas fa-expand-alt me-1"></i>展开ID
                                </button>
                                <button class="btn btn-outline-light btn-sm" onclick="collapseAllIds()" title="折叠所有ID">
                                    <i class="fas fa-compress-alt me-1"></i>折叠ID
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 80px;" class="text-center">ID</th>
                                        <th>视频标题</th>
                                        <th style="width: 100px;" class="text-center">状态</th>
                                        <th style="width: 120px;" class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody id="demoTableBody">
                                    <!-- 动态生成的演示数据 -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📖 使用说明</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-mouse-pointer text-primary me-2"></i>
                                <strong>单击ID：</strong>展开或折叠完整ID显示
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-copy text-success me-2"></i>
                                <strong>双击ID：</strong>快速复制ID到剪贴板
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-expand-alt text-info me-2"></i>
                                <strong>批量展开：</strong>点击"展开ID"按钮展开所有ID
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-compress-alt text-warning me-2"></i>
                                <strong>批量折叠：</strong>点击"折叠ID"按钮折叠所有ID
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🎯 设计优势</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-eye text-primary me-2"></i>
                                <strong>界面简洁：</strong>默认折叠，节省页面空间
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-tachometer-alt text-success me-2"></i>
                                <strong>操作便捷：</strong>单击展开，双击复制
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-mobile-alt text-info me-2"></i>
                                <strong>响应式：</strong>适配移动端和桌面端
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-palette text-warning me-2"></i>
                                <strong>视觉反馈：</strong>悬停效果和动画过渡
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>提示：</strong>此演示页面模拟了admin管理页面中的ID折叠功能。在实际使用中，ID为18位随机字符串，包含大小写字母和数字。
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟视频数据
        const demoVideos = [
            { id: 'tJPesWXD2wmwL2Qn7o', title: '春季养生指南', status: '启用' },
            { id: 'ahfa2nja66cDhl8Aco', title: '健康饮食搭配', status: '启用' },
            { id: 'Hjn4J0RkPPFavRrhcz', title: '运动健身教程', status: '禁用' },
            { id: 'mK8pLx3vN9qR2sT7uY', title: '瑜伽入门基础', status: '启用' },
            { id: 'bC5dE8fG1hI4jK7lM0', title: '营养搭配技巧', status: '启用' }
        ];

        // 生成演示表格
        function generateDemoTable() {
            const tbody = document.getElementById('demoTableBody');
            tbody.innerHTML = '';

            demoVideos.forEach((video, index) => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td class="text-center" style="padding: 8px 4px;">
                        <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                             data-full-id="${video.id}"
                             onclick="toggleIdDisplay(this)"
                             ondblclick="copyVideoId('${video.id}')"
                             title="单击展开/折叠，双击复制ID">
                            <span class="id-display-text">${video.id.substring(0, 4)}...</span>
                            <i class="fas fa-chevron-down id-toggle-icon"></i>
                            <div class="id-full-text" style="display: none;">${video.id}</div>
                        </div>
                    </td>
                    <td>${video.title}</td>
                    <td class="text-center">
                        <span class="badge ${video.status === '启用' ? 'bg-success' : 'bg-secondary'}">
                            ${video.status}
                        </span>
                    </td>
                    <td class="text-center">
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-primary" title="播放">
                                <i class="fas fa-play"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-warning" title="编辑">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" title="删除">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // ID展开/折叠功能
        function toggleIdDisplay(element) {
            const isExpanded = element.classList.contains('id-expanded');
            const fullId = element.dataset.fullId;
            const displayText = element.querySelector('.id-display-text');
            const fullText = element.querySelector('.id-full-text');
            const icon = element.querySelector('.id-toggle-icon');
            
            if (isExpanded) {
                // 折叠ID
                element.classList.remove('id-expanded');
                element.classList.add('id-collapsed');
                displayText.textContent = fullId.substring(0, 4) + '...';
                displayText.style.display = 'inline';
                fullText.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
                element.title = '单击展开/折叠，双击复制ID';
            } else {
                // 展开ID
                element.classList.remove('id-collapsed');
                element.classList.add('id-expanded');
                displayText.style.display = 'none';
                fullText.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
                element.title = '单击展开/折叠，双击复制ID';
            }
        }

        // 批量折叠所有ID
        function collapseAllIds() {
            const idContainers = document.querySelectorAll('.id-container.id-expanded');
            idContainers.forEach(container => {
                toggleIdDisplay(container);
            });
        }

        // 批量展开所有ID
        function expandAllIds() {
            const idContainers = document.querySelectorAll('.id-container.id-collapsed');
            idContainers.forEach(container => {
                toggleIdDisplay(container);
            });
        }

        // 复制ID到剪贴板
        function copyVideoId(videoId) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(videoId).then(() => {
                    showAlert('视频ID已复制到剪贴板: ' + videoId, 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    showAlert('复制失败，请手动复制', 'error');
                });
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = videoId;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    showAlert('视频ID已复制到剪贴板: ' + videoId, 'success');
                } catch (err) {
                    console.error('复制失败:', err);
                    showAlert('复制失败，请手动复制', 'error');
                }
                document.body.removeChild(textArea);
            }
        }

        // 显示提示消息
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }

        // 页面加载时生成演示表格
        document.addEventListener('DOMContentLoaded', function() {
            generateDemoTable();
        });
    </script>
</body>
</html>
