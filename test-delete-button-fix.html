<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试删除按钮修复</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        .clickable-status {
            transition: all 0.2s ease;
            user-select: none;
            cursor: pointer;
        }
        
        .clickable-status:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }
        
        .demo-row {
            border: 1px solid #dee2e6;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        
        .demo-actions {
            display: flex;
            gap: 5px;
            justify-content: center;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">测试删除按钮修复</h1>
        
        <div class="alert alert-success">
            <h5>✅ 修复的问题：</h5>
            <ul class="mb-0">
                <li>每次状态切换不再生成新的删除按钮</li>
                <li>删除按钮始终存在，只是根据状态显示/隐藏</li>
                <li>使用CSS控制显示状态，避免DOM操作错误</li>
                <li>简化了JavaScript逻辑，提高了稳定性</li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h3>问题演示与修复</h3>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">模拟视频行</h5>
                    </div>
                    <div class="card-body">
                        <!-- 模拟视频行1 - 启用状态 -->
                        <div class="demo-row" id="demo-row-1">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>测试视频 1</strong>
                                    <br><small class="text-muted">ID: 1</small>
                                </div>
                                <div>
                                    <span class="status-badge badge bg-success clickable-status" 
                                          onclick="demoToggle(1, true)"
                                          title="点击切换为禁用状态">
                                        <i class="fas fa-check me-1"></i>启用
                                    </span>
                                </div>
                                <div class="demo-actions">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-btn" 
                                            style="display: none;"
                                            title="永久删除视频">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 模拟视频行2 - 禁用状态 -->
                        <div class="demo-row" id="demo-row-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>测试视频 2</strong>
                                    <br><small class="text-muted">ID: 2</small>
                                </div>
                                <div>
                                    <span class="status-badge badge bg-secondary clickable-status" 
                                          onclick="demoToggle(2, false)"
                                          title="点击切换为启用状态">
                                        <i class="fas fa-pause me-1"></i>禁用
                                    </span>
                                </div>
                                <div class="demo-actions">
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-btn" 
                                            style="display: inline-block;"
                                            title="永久删除视频">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>测试说明：</strong>点击状态标识切换状态，观察删除按钮的显示/隐藏效果。
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>修复详情</h3>
                
                <div class="card mb-3">
                    <div class="card-header">
                        <h5 class="mb-0">修复前的问题</h5>
                    </div>
                    <div class="card-body">
                        <ul class="text-danger">
                            <li>每次状态切换都会创建新的删除按钮</li>
                            <li>DOM中会出现重复的删除按钮元素</li>
                            <li>JavaScript查找按钮逻辑复杂且不稳定</li>
                            <li>可能导致事件绑定混乱</li>
                        </ul>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">修复后的改进</h5>
                    </div>
                    <div class="card-body">
                        <ul class="text-success">
                            <li>删除按钮始终存在，只控制显示状态</li>
                            <li>使用CSS的display属性控制显示/隐藏</li>
                            <li>添加了.delete-btn类便于精确查找</li>
                            <li>简化了JavaScript逻辑，提高稳定性</li>
                        </ul>
                    </div>
                </div>
                
                <div class="mt-3">
                    <button class="btn btn-primary" onclick="testAdminPage()">
                        <i class="fas fa-external-link-alt me-1"></i>测试Admin页面
                    </button>
                </div>
                
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="mb-0">测试日志</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" style="height: 200px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <p class="text-muted">点击状态标识查看测试结果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>技术实现对比</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>方面</th>
                                <th>修复前</th>
                                <th>修复后</th>
                                <th>优势</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>HTML结构</strong></td>
                                <td>使用 <code>th:if</code> 条件渲染</td>
                                <td>始终渲染，用 <code>th:style</code> 控制显示</td>
                                <td>DOM结构稳定</td>
                            </tr>
                            <tr>
                                <td><strong>JavaScript逻辑</strong></td>
                                <td>动态创建/删除DOM元素</td>
                                <td>只修改CSS display属性</td>
                                <td>逻辑简单，性能更好</td>
                            </tr>
                            <tr>
                                <td><strong>元素查找</strong></td>
                                <td>复杂的选择器和循环查找</td>
                                <td>使用 <code>.delete-btn</code> 类直接查找</td>
                                <td>查找精确，避免错误</td>
                            </tr>
                            <tr>
                                <td><strong>事件绑定</strong></td>
                                <td>需要重新绑定新创建的元素</td>
                                <td>事件绑定保持不变</td>
                                <td>避免事件丢失</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🔧 核心修复代码</h5>
                    </div>
                    <div class="card-body">
                        <h6>HTML模板修改：</h6>
                        <pre class="bg-light p-3"><code>&lt;!-- 修复前 --&gt;
&lt;button th:if="${!video.isActive}" class="btn btn-outline-danger"&gt;

&lt;!-- 修复后 --&gt;
&lt;button class="btn btn-outline-danger delete-btn"
        th:style="${video.isActive ? 'display: none;' : 'display: inline-block;'}"&gt;</code></pre>
                        
                        <h6>JavaScript修改：</h6>
                        <pre class="bg-light p-3"><code>// 修复前：复杂的查找和创建逻辑
const deleteButtons = videoRow.querySelectorAll('.btn-outline-danger');
// ... 复杂的循环查找和创建逻辑

// 修复后：简单的显示/隐藏
const deleteButton = videoRow.querySelector('.delete-btn');
deleteButton.style.display = newStatus ? 'none' : 'inline-block';</code></pre>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function demoToggle(videoId, currentStatus) {
            const newStatus = !currentStatus;
            const demoRow = document.getElementById(`demo-row-${videoId}`);
            
            log(`视频${videoId}: ${currentStatus ? '启用' : '禁用'} → ${newStatus ? '启用' : '禁用'}`, 'info');
            
            // 更新状态标识
            const statusBadge = demoRow.querySelector('.clickable-status');
            if (newStatus) {
                statusBadge.className = 'status-badge badge bg-success clickable-status';
                statusBadge.innerHTML = '<i class="fas fa-check me-1"></i>启用';
                statusBadge.title = '点击切换为禁用状态';
                statusBadge.onclick = () => demoToggle(videoId, true);
            } else {
                statusBadge.className = 'status-badge badge bg-secondary clickable-status';
                statusBadge.innerHTML = '<i class="fas fa-pause me-1"></i>禁用';
                statusBadge.title = '点击切换为启用状态';
                statusBadge.onclick = () => demoToggle(videoId, false);
            }
            
            // 更新删除按钮显示（这里是关键修复）
            const deleteButton = demoRow.querySelector('.delete-btn');
            if (deleteButton) {
                if (newStatus) {
                    deleteButton.style.display = 'none';
                    log('✅ 删除按钮已隐藏（启用状态）', 'success');
                } else {
                    deleteButton.style.display = 'inline-block';
                    log('✅ 删除按钮已显示（禁用状态）', 'success');
                }
                
                // 检查是否有重复按钮
                const allDeleteButtons = demoRow.querySelectorAll('.delete-btn');
                if (allDeleteButtons.length > 1) {
                    log(`❌ 发现${allDeleteButtons.length}个删除按钮！`, 'error');
                } else {
                    log('✅ 删除按钮数量正常（1个）', 'success');
                }
            }
        }

        function testAdminPage() {
            log('打开Admin页面进行实际测试...', 'info');
            log('测试步骤：', 'info');
            log('1. 多次点击同一个视频的状态标识', 'info');
            log('2. 检查删除按钮是否正常显示/隐藏', 'info');
            log('3. 确认没有生成重复的删除按钮', 'info');
            window.open('/admin', '_blank');
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            log('删除按钮修复测试页面加载完成', 'info');
            log('现在删除按钮不会重复生成了', 'success');
            log('试试多次点击上面的状态标识', 'info');
        });
    </script>
</body>
</html>
