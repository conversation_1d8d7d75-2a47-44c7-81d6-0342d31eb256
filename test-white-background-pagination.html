<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>白色背景分页测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        /* 复制videos-style.css中的分页样式 */
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #667eea;
            font-weight: 500;
        }

        /* page-item active下的page-link字体设置为白色 */
        .pagination .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        /* page-item disabled状态样式 - 背景设置为白色 */
        .pagination .page-item.disabled .page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        /* disabled状态的span元素样式（与disabled的a元素保持一致） */
        .pagination .page-item.disabled span.page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #6c757d !important;
            cursor: not-allowed !important;
        }
        
        .background-demo {
            padding: 20px;
            margin: 15px 0;
            border-radius: 8px;
            border: 2px dashed #ccc;
        }
        
        .bg-gray {
            background: #f8f9fa;
        }
        
        .bg-blue {
            background: #e3f2fd;
        }
        
        .bg-dark {
            background: #343a40;
        }
        
        .color-info {
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-top: 10px;
            padding: 8px;
            background: rgba(0,0,0,0.05);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">page-item disabled 白色背景测试</h1>
        
        <!-- 白色背景测试 -->
        <div class="test-section">
            <h3 class="test-title">白色背景效果测试</h3>
            <p>测试disabled状态的分页按钮在不同背景下的显示效果</p>
            
            <!-- 默认白色背景 -->
            <div class="background-demo">
                <h5>默认白色背景</h5>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <div class="color-info">
                    背景色: #ffffff (白色)<br>
                    边框色: #dee2e6 (浅灰)<br>
                    文字色: #6c757d (中灰)
                </div>
            </div>
            
            <!-- 灰色背景对比 -->
            <div class="background-demo bg-gray">
                <h5>灰色背景对比</h5>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">2</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <div class="color-info">
                    容器背景: #f8f9fa (浅灰)<br>
                    按钮背景: #ffffff (白色) - 形成对比
                </div>
            </div>
            
            <!-- 蓝色背景对比 -->
            <div class="background-demo bg-blue">
                <h5>蓝色背景对比</h5>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">3</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <div class="color-info">
                    容器背景: #e3f2fd (浅蓝)<br>
                    按钮背景: #ffffff (白色) - 清晰可见
                </div>
            </div>
            
            <!-- 深色背景对比 -->
            <div class="background-demo bg-dark">
                <h5 style="color: white;">深色背景对比</h5>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">4</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <div class="color-info" style="background: rgba(255,255,255,0.1); color: white;">
                    容器背景: #343a40 (深灰)<br>
                    按钮背景: #ffffff (白色) - 强烈对比
                </div>
            </div>
        </div>
        
        <!-- 状态对比 -->
        <div class="test-section">
            <h3 class="test-title">所有状态对比</h3>
            <p>对比正常、激活、禁用三种状态的视觉效果</p>
            
            <div class="d-flex justify-content-center gap-4 flex-wrap">
                <!-- 正常状态 -->
                <div class="text-center">
                    <p><strong>正常状态</strong></p>
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                    </ul>
                    <small class="text-muted">可点击，蓝色文字</small>
                </div>
                
                <!-- 激活状态 -->
                <div class="text-center">
                    <p><strong>激活状态</strong></p>
                    <ul class="pagination">
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                    </ul>
                    <small class="text-muted">蓝色背景，白色文字</small>
                </div>
                
                <!-- 禁用状态 -->
                <div class="text-center">
                    <p><strong>禁用状态</strong></p>
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                    <small class="text-muted">白色背景，灰色文字</small>
                </div>
            </div>
        </div>
        
        <!-- CSS代码展示 -->
        <div class="test-section">
            <h3 class="test-title">CSS代码</h3>
            <p>以下是实现白色背景的CSS代码：</p>

            <pre style="background: #f8f9fa; padding: 15px; border-radius: 6px; overflow-x: auto;"><code>/* page-item disabled状态样式 - 背景设置为白色 */
.pagination .page-item.disabled .page-link {
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
    cursor: not-allowed !important;
    pointer-events: none !important;
}

/* disabled状态的span元素样式（与disabled的a元素保持一致） */
.pagination .page-item.disabled span.page-link {
    background: #ffffff !important;
    border-color: #dee2e6 !important;
    color: #6c757d !important;
    cursor: not-allowed !important;
}</code></pre>

            <div class="alert alert-success mt-3">
                <h5><i class="fas fa-check-circle"></i> 修改完成</h5>
                <p class="mb-0">
                    ✅ 已在 <code>videos-style.css</code> 中添加白色背景样式<br>
                    ✅ 已在 <code>style.css</code> 中添加全局白色背景样式<br>
                    ✅ 确保所有页面的分页按钮disabled状态都使用白色背景
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
