<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试删除功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">调试删除功能</h1>
        
        <div class="alert alert-warning">
            <h5>🐛 问题描述：</h5>
            <p>删除不起作用，确认删除后视频仍然存在</p>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h3>删除功能测试</h3>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">步骤化测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="testVideoId" class="form-label">测试视频ID：</label>
                            <input type="number" class="form-control" id="testVideoId" placeholder="输入要测试的视频ID">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-primary" onclick="step1_getVideoInfo()">
                                <i class="fas fa-info-circle me-1"></i>步骤1: 获取视频信息
                            </button>
                            <button class="btn btn-warning" onclick="step2_disableVideo()">
                                <i class="fas fa-pause me-1"></i>步骤2: 禁用视频
                            </button>
                            <button class="btn btn-danger" onclick="step3_deleteVideo()">
                                <i class="fas fa-trash me-1"></i>步骤3: 删除视频
                            </button>
                            <button class="btn btn-info" onclick="step4_verifyDeletion()">
                                <i class="fas fa-search me-1"></i>步骤4: 验证删除结果
                            </button>
                        </div>
                        
                        <div class="mt-3">
                            <button class="btn btn-secondary" onclick="getAllVideos()">
                                <i class="fas fa-list me-1"></i>获取所有视频列表
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>调试日志</h3>
                <div class="card">
                    <div class="card-body">
                        <div id="debugLog" style="height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace; font-size: 0.9em;">
                            <p class="text-muted">等待测试开始...</p>
                        </div>
                        <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearLog()">清空日志</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>可能的问题原因</h3>
                <div class="accordion" id="problemAccordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#problem1">
                                1. 前端JavaScript错误
                            </button>
                        </h2>
                        <div id="problem1" class="accordion-collapse collapse show" data-bs-parent="#problemAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>AdminApi.deleteVideo方法调用失败</li>
                                    <li>网络请求被阻止或超时</li>
                                    <li>响应解析错误</li>
                                    <li>事件监听器未正确绑定</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#problem2">
                                2. 后端API问题
                            </button>
                        </h2>
                        <div id="problem2" class="accordion-collapse collapse" data-bs-parent="#problemAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>视频状态检查失败（视频仍为启用状态）</li>
                                    <li>数据库删除操作失败</li>
                                    <li>事务回滚</li>
                                    <li>权限问题</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    
                    <div class="accordion-item">
                        <h2 class="accordion-header">
                            <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#problem3">
                                3. 数据库问题
                            </button>
                        </h2>
                        <div id="problem3" class="accordion-collapse collapse" data-bs-parent="#problemAccordion">
                            <div class="accordion-body">
                                <ul>
                                    <li>外键约束阻止删除</li>
                                    <li>数据库连接问题</li>
                                    <li>软删除vs硬删除混淆</li>
                                    <li>缓存问题</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let currentVideoInfo = null;

        function log(message, type = 'info') {
            const logDiv = document.getElementById('debugLog');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            logDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function clearLog() {
            document.getElementById('debugLog').innerHTML = '';
        }

        function getVideoId() {
            const videoId = document.getElementById('testVideoId').value;
            if (!videoId) {
                log('❌ 请先输入视频ID', 'error');
                return null;
            }
            return videoId;
        }

        async function step1_getVideoInfo() {
            const videoId = getVideoId();
            if (!videoId) return;

            log(`🔍 步骤1: 获取视频 ${videoId} 的信息...`, 'info');
            
            try {
                const response = await fetch(`/api/videos/${videoId}`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    currentVideoInfo = result.data;
                    log(`✅ 视频信息获取成功:`, 'success');
                    log(`   - 标题: ${result.data.title}`, 'info');
                    log(`   - 状态: ${result.data.isActive ? '启用' : '禁用'}`, 'info');
                    log(`   - 创建时间: ${new Date(result.data.createdTime).toLocaleString()}`, 'info');
                    
                    if (result.data.isActive) {
                        log(`⚠️ 视频当前为启用状态，需要先禁用才能删除`, 'warning');
                    } else {
                        log(`✅ 视频已禁用，可以进行删除操作`, 'success');
                    }
                } else {
                    log(`❌ 获取视频信息失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function step2_disableVideo() {
            const videoId = getVideoId();
            if (!videoId) return;

            if (!currentVideoInfo) {
                log('⚠️ 请先执行步骤1获取视频信息', 'warning');
                return;
            }

            if (!currentVideoInfo.isActive) {
                log('ℹ️ 视频已经是禁用状态', 'info');
                return;
            }

            log(`🔄 步骤2: 禁用视频 ${videoId}...`, 'info');
            
            try {
                const response = await fetch(`/api/videos/${videoId}/toggle-status`, {
                    method: 'PUT'
                });
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ 视频禁用成功`, 'success');
                    currentVideoInfo.isActive = false;
                } else {
                    log(`❌ 视频禁用失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function step3_deleteVideo() {
            const videoId = getVideoId();
            if (!videoId) return;

            if (!currentVideoInfo) {
                log('⚠️ 请先执行步骤1获取视频信息', 'warning');
                return;
            }

            if (currentVideoInfo.isActive) {
                log('❌ 视频仍为启用状态，无法删除。请先执行步骤2', 'error');
                return;
            }

            log(`🗑️ 步骤3: 删除视频 ${videoId}...`, 'warning');
            
            try {
                log(`📡 发送DELETE请求到: /api/videos/${videoId}`, 'info');
                
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'DELETE'
                });
                
                log(`📨 响应状态: ${response.status} ${response.statusText}`, 'info');
                
                const result = await response.json();
                log(`📄 响应内容: ${JSON.stringify(result, null, 2)}`, 'info');
                
                if (result.success) {
                    log(`✅ 删除请求成功: ${result.message}`, 'success');
                } else {
                    log(`❌ 删除请求失败: ${result.message}`, 'error');
                    if (result.code) {
                        log(`   错误代码: ${result.code}`, 'error');
                    }
                }
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        async function step4_verifyDeletion() {
            const videoId = getVideoId();
            if (!videoId) return;

            log(`🔍 步骤4: 验证视频 ${videoId} 是否已删除...`, 'info');
            
            try {
                const response = await fetch(`/api/videos/${videoId}`);
                const result = await response.json();
                
                if (response.status === 404 || !result.success) {
                    log(`✅ 验证成功: 视频已被删除`, 'success');
                } else if (result.success && result.data) {
                    log(`❌ 验证失败: 视频仍然存在`, 'error');
                    log(`   - 标题: ${result.data.title}`, 'error');
                    log(`   - 状态: ${result.data.isActive ? '启用' : '禁用'}`, 'error');
                } else {
                    log(`⚠️ 验证结果不明确: ${JSON.stringify(result)}`, 'warning');
                }
            } catch (error) {
                log(`❌ 验证过程中发生错误: ${error.message}`, 'error');
            }
        }

        async function getAllVideos() {
            log(`📋 获取所有视频列表...`, 'info');
            
            try {
                const response = await fetch('/api/videos?page=0&size=20');
                const result = await response.json();
                
                if (result.success && result.data) {
                    log(`✅ 获取到 ${result.data.length} 个视频:`, 'success');
                    result.data.forEach((video, index) => {
                        const status = video.isActive ? '启用' : '禁用';
                        log(`   ${index + 1}. ID:${video.id} - ${video.title} (${status})`, 'info');
                    });
                } else {
                    log(`❌ 获取视频列表失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 网络错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            log('🔧 删除功能调试工具已加载', 'info');
            log('请按照步骤进行测试:', 'info');
            log('1. 输入要测试的视频ID', 'info');
            log('2. 按顺序执行各个步骤', 'info');
            log('3. 观察每个步骤的详细日志', 'info');
        });
    </script>
</body>
</html>
