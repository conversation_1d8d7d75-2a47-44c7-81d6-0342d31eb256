<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ID折叠功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        /* ID折叠样式 */
        .id-container {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .id-collapsed {
            max-width: 50px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .id-expanded {
            max-width: none;
            word-break: break-all;
        }

        .id-toggle-icon {
            font-size: 0.8em;
            margin-left: 4px;
            transition: transform 0.3s ease;
        }

        .id-expanded .id-toggle-icon {
            transform: rotate(180deg);
        }

        .id-container:hover {
            background-color: #e9ecef !important;
            transform: scale(1.02);
        }

        .id-full-text {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <h2 class="text-center mb-4">ID折叠功能测试</h2>
        
        <div class="row mb-3">
            <div class="col-12 text-center">
                <button class="btn btn-primary me-2" onclick="expandAllIds()">
                    <i class="fas fa-expand-alt me-1"></i>展开所有ID
                </button>
                <button class="btn btn-secondary" onclick="collapseAllIds()">
                    <i class="fas fa-compress-alt me-1"></i>折叠所有ID
                </button>
            </div>
        </div>

        <div class="card">
            <div class="card-body">
                <table class="table">
                    <thead>
                        <tr>
                            <th style="width: 100px;">ID</th>
                            <th>标题</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>
                                <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                                     data-full-id="tJPesWXD2wmwL2Qn7o"
                                     onclick="toggleIdDisplay(this)"
                                     ondblclick="copyVideoId(this.dataset.fullId)"
                                     title="单击展开/折叠，双击复制ID">
                                    <span class="id-display-text">tJPe...</span>
                                    <i class="fas fa-chevron-down id-toggle-icon"></i>
                                    <div class="id-full-text" style="display: none;">tJPesWXD2wmwL2Qn7o</div>
                                </div>
                            </td>
                            <td>春季养生指南</td>
                            <td><span class="badge bg-success">启用</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                                     data-full-id="ahfa2nja66cDhl8Aco"
                                     onclick="toggleIdDisplay(this)"
                                     ondblclick="copyVideoId(this.dataset.fullId)"
                                     title="单击展开/折叠，双击复制ID">
                                    <span class="id-display-text">ahfa...</span>
                                    <i class="fas fa-chevron-down id-toggle-icon"></i>
                                    <div class="id-full-text" style="display: none;">ahfa2nja66cDhl8Aco</div>
                                </div>
                            </td>
                            <td>健康饮食搭配</td>
                            <td><span class="badge bg-success">启用</span></td>
                        </tr>
                        <tr>
                            <td>
                                <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                                     data-full-id="Hjn4J0RkPPFavRrhcz"
                                     onclick="toggleIdDisplay(this)"
                                     ondblclick="copyVideoId(this.dataset.fullId)"
                                     title="单击展开/折叠，双击复制ID">
                                    <span class="id-display-text">Hjn4...</span>
                                    <i class="fas fa-chevron-down id-toggle-icon"></i>
                                    <div class="id-full-text" style="display: none;">Hjn4J0RkPPFavRrhcz</div>
                                </div>
                            </td>
                            <td>运动健身教程</td>
                            <td><span class="badge bg-secondary">禁用</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="alert alert-info mt-4">
            <h5>测试说明：</h5>
            <ul class="mb-0">
                <li>单击ID可以展开/折叠显示</li>
                <li>双击ID可以复制到剪贴板</li>
                <li>使用上方按钮可以批量展开/折叠所有ID</li>
                <li>悬停时会有视觉反馈效果</li>
            </ul>
        </div>
    </div>

    <script>
        /**
         * ID展开/折叠功能
         */
        function toggleIdDisplay(element) {
            const isExpanded = element.classList.contains('id-expanded');
            const fullId = element.dataset.fullId;
            const displayText = element.querySelector('.id-display-text');
            const fullText = element.querySelector('.id-full-text');
            const icon = element.querySelector('.id-toggle-icon');
            
            if (isExpanded) {
                // 折叠ID
                element.classList.remove('id-expanded');
                element.classList.add('id-collapsed');
                displayText.textContent = fullId.substring(0, 4) + '...';
                displayText.style.display = 'inline';
                fullText.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
                element.title = '单击展开/折叠，双击复制ID';
            } else {
                // 展开ID
                element.classList.remove('id-collapsed');
                element.classList.add('id-expanded');
                displayText.style.display = 'none';
                fullText.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
                element.title = '单击展开/折叠，双击复制ID';
            }
        }

        /**
         * 批量折叠所有ID
         */
        function collapseAllIds() {
            const idContainers = document.querySelectorAll('.id-container.id-expanded');
            idContainers.forEach(container => {
                toggleIdDisplay(container);
            });
        }

        /**
         * 批量展开所有ID
         */
        function expandAllIds() {
            const idContainers = document.querySelectorAll('.id-container.id-collapsed');
            idContainers.forEach(container => {
                toggleIdDisplay(container);
            });
        }

        /**
         * 复制ID到剪贴板
         */
        function copyVideoId(videoId) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(videoId).then(() => {
                    showAlert('视频ID已复制到剪贴板: ' + videoId, 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    showAlert('复制失败，请手动复制', 'error');
                });
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = videoId;
                document.body.appendChild(textArea);
                textArea.select();
                try {
                    document.execCommand('copy');
                    showAlert('视频ID已复制到剪贴板: ' + videoId, 'success');
                } catch (err) {
                    console.error('复制失败:', err);
                    showAlert('复制失败，请手动复制', 'error');
                }
                document.body.removeChild(textArea);
            }
        }

        /**
         * 显示提示消息
         */
        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            document.body.appendChild(alertDiv);
            
            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.remove();
                }
            }, 3000);
        }
    </script>
</body>
</html>
