<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>4位ID折叠演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .id-container {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .id-collapsed {
            max-width: 50px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .id-expanded {
            max-width: none;
            word-break: break-all;
        }

        .id-toggle-icon {
            font-size: 0.8em;
            margin-left: 4px;
            transition: transform 0.3s ease;
        }

        .id-expanded .id-toggle-icon {
            transform: rotate(180deg);
        }

        .id-container:hover {
            background-color: #e9ecef !important;
            transform: scale(1.02);
        }

        .id-full-text {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }

        .comparison-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🆔 4位ID折叠演示</h1>
                <p class="text-center text-muted">更紧凑的ID显示方案</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card comparison-card">
                    <div class="card-body">
                        <h5 class="text-center mb-4">📊 显示对比</h5>
                        <div class="row text-center">
                            <div class="col-md-6">
                                <h6>6位显示 (旧版)</h6>
                                <div class="badge bg-light text-dark mb-2">tJPesW...</div>
                                <p class="small mb-0">占用约60px宽度</p>
                            </div>
                            <div class="col-md-6">
                                <h6>4位显示 (新版)</h6>
                                <div class="badge bg-light text-dark mb-2">tJPe...</div>
                                <p class="small mb-0">占用约50px宽度</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0">📋 4位ID演示表格</h5>
                            <div class="btn-group" role="group">
                                <button class="btn btn-outline-light btn-sm" onclick="expandAllIds()">
                                    <i class="fas fa-expand-alt me-1"></i>展开
                                </button>
                                <button class="btn btn-outline-light btn-sm" onclick="collapseAllIds()">
                                    <i class="fas fa-compress-alt me-1"></i>折叠
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 60px;" class="text-start">ID</th>
                                        <th>视频标题</th>
                                        <th style="width: 80px;" class="text-center">状态</th>
                                        <th style="width: 100px;" class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="text-start">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                                                 data-full-id="tJPesWXD2wmwL2Qn7o"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('tJPesWXD2wmwL2Qn7o')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">tJPe...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">tJPesWXD2wmwL2Qn7o</div>
                                            </div>
                                        </td>
                                        <td>春季养生指南</td>
                                        <td class="text-center"><span class="badge bg-success">启用</span></td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-start">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed"
                                                 data-full-id="ahfa2nja66cDhl8Aco"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('ahfa2nja66cDhl8Aco')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">ahfa...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">ahfa2nja66cDhl8Aco</div>
                                            </div>
                                        </td>
                                        <td>健康饮食搭配</td>
                                        <td class="text-center"><span class="badge bg-success">启用</span></td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-start">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed"
                                                 data-full-id="Hjn4J0RkPPFavRrhcz"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('Hjn4J0RkPPFavRrhcz')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">Hjn4...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">Hjn4J0RkPPFavRrhcz</div>
                                            </div>
                                        </td>
                                        <td>运动健身教程</td>
                                        <td class="text-center"><span class="badge bg-secondary">禁用</span></td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-start">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed"
                                                 data-full-id="mK8pLx3vN9qR2sT7uY"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('mK8pLx3vN9qR2sT7uY')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">mK8p...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">mK8pLx3vN9qR2sT7uY</div>
                                            </div>
                                        </td>
                                        <td>瑜伽入门基础</td>
                                        <td class="text-center"><span class="badge bg-success">启用</span></td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-start">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed"
                                                 data-full-id="bC5dE8fG1hI4jK7lM0"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('bC5dE8fG1hI4jK7lM0')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">bC5d...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">bC5dE8fG1hI4jK7lM0</div>
                                            </div>
                                        </td>
                                        <td>营养搭配技巧</td>
                                        <td class="text-center"><span class="badge bg-success">启用</span></td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">✨ 4位显示优势</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-compress text-primary me-2"></i>
                                <strong>更紧凑：</strong>节省更多页面空间
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-eye text-success me-2"></i>
                                <strong>更清晰：</strong>减少视觉干扰
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-mobile-alt text-info me-2"></i>
                                <strong>移动友好：</strong>在小屏幕上表现更好
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-tachometer-alt text-warning me-2"></i>
                                <strong>快速识别：</strong>4位字符足以区分
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📱 使用说明</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-mouse-pointer text-primary me-2"></i>
                                <strong>单击：</strong>展开/折叠完整ID
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-copy text-success me-2"></i>
                                <strong>双击：</strong>复制ID到剪贴板
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-expand-alt text-info me-2"></i>
                                <strong>批量展开：</strong>点击"展开"按钮
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-compress-alt text-warning me-2"></i>
                                <strong>批量折叠：</strong>点击"折叠"按钮
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i>
                    <strong>更新完成！</strong>ID默认显示已从6位调整为4位，界面更加紧凑整洁。
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleIdDisplay(element) {
            const isExpanded = element.classList.contains('id-expanded');
            const fullId = element.dataset.fullId;
            const displayText = element.querySelector('.id-display-text');
            const fullText = element.querySelector('.id-full-text');
            const icon = element.querySelector('.id-toggle-icon');
            
            if (isExpanded) {
                element.classList.remove('id-expanded');
                element.classList.add('id-collapsed');
                displayText.textContent = fullId.substring(0, 4) + '...';
                displayText.style.display = 'inline';
                fullText.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                element.classList.remove('id-collapsed');
                element.classList.add('id-expanded');
                displayText.style.display = 'none';
                fullText.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        }

        function collapseAllIds() {
            const idContainers = document.querySelectorAll('.id-container.id-expanded');
            idContainers.forEach(container => {
                toggleIdDisplay(container);
            });
        }

        function expandAllIds() {
            const idContainers = document.querySelectorAll('.id-container.id-collapsed');
            idContainers.forEach(container => {
                toggleIdDisplay(container);
            });
        }

        function copyVideoId(videoId) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(videoId).then(() => {
                    showAlert('视频ID已复制: ' + videoId, 'success');
                });
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = videoId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('视频ID已复制: ' + videoId, 'success');
            }
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
            document.body.appendChild(alertDiv);
            setTimeout(() => alertDiv.remove(), 3000);
        }
    </script>
</body>
</html>
