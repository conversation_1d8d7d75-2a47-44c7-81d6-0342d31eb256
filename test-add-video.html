<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试添加视频功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <h1>测试添加视频功能</h1>
    
    <form id="testForm">
        <div class="form-group">
            <label for="title">视频标题 *</label>
            <input type="text" id="title" name="title" value="测试视频" required>
        </div>
        
        <div class="form-group">
            <label for="description">视频描述</label>
            <textarea id="description" name="description" rows="3">这是一个测试视频</textarea>
        </div>
        
        <div class="form-group">
            <label for="videoUrl">视频URL *</label>
            <input type="url" id="videoUrl" name="videoUrl" 
                   value="https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4" required>
        </div>
        
        <div class="form-group">
            <label for="thumbnailUrl">缩略图URL</label>
            <input type="url" id="thumbnailUrl" name="thumbnailUrl" 
                   value="https://sample-videos.com/zip/10/jpg/SampleJPGImage_1280x720_1mb.jpg">
        </div>
        
        <div class="form-group">
            <label for="videoFormat">视频格式</label>
            <select id="videoFormat" name="videoFormat">
                <option value="">自动检测</option>
                <option value="mp4" selected>MP4</option>
                <option value="avi">AVI</option>
                <option value="mov">MOV</option>
                <option value="wmv">WMV</option>
                <option value="flv">FLV</option>
                <option value="webm">WebM</option>
            </select>
        </div>
        
        <div class="form-group">
            <label for="resolution">分辨率</label>
            <select id="resolution" name="resolution">
                <option value="">请选择</option>
                <option value="4K">4K (3840×2160)</option>
                <option value="1080p">1080p (1920×1080)</option>
                <option value="720p" selected>720p (1280×720)</option>
                <option value="480p">480p (854×480)</option>
                <option value="360p">360p (640×360)</option>
            </select>
        </div>
        
        <button type="submit">添加视频</button>
    </form>
    
    <div id="result"></div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '正在提交...';
            
            const formData = new FormData(this);
            const videoData = {
                title: formData.get('title'),
                description: formData.get('description'),
                videoUrl: formData.get('videoUrl'),
                thumbnailUrl: formData.get('thumbnailUrl') || null,
                videoFormat: formData.get('videoFormat') || null,
                resolution: formData.get('resolution') || null
            };
            
            try {
                const response = await fetch('/api/videos', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(videoData)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = `
                        <h3>添加成功！</h3>
                        <p>视频ID: ${result.data.id}</p>
                        <p>标题: ${result.data.title}</p>
                        <p>创建时间: ${result.data.createdTime}</p>
                    `;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.innerHTML = `
                        <h3>添加失败</h3>
                        <p>错误信息: ${result.message || '未知错误'}</p>
                        <p>状态码: ${response.status}</p>
                    `;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `
                    <h3>请求失败</h3>
                    <p>错误信息: ${error.message}</p>
                    <p>请确保服务器正在运行在 http://localhost:8083</p>
                `;
            }
        });
    </script>
</body>
</html>
