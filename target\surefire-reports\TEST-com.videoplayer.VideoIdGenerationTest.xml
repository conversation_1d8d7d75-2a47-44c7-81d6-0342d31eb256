<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="com.videoplayer.VideoIdGenerationTest" time="0.262" tests="4" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="17"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="GBK"/>
    <property name="java.class.path" value="D:\admin\desktop\video-player-project\target\test-classes;D:\admin\desktop\video-player-project\target\classes;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-data-jpa\3.2.0\spring-boot-starter-data-jpa-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-aop\3.2.0\spring-boot-starter-aop-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\aspectj\aspectjweaver\1.9.20.1\aspectjweaver-1.9.20.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.0\spring-boot-starter-jdbc-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-jdbc\6.1.1\spring-jdbc-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hibernate\orm\hibernate-core\6.3.1.Final\hibernate-core-6.3.1.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\smallrye\jandex\3.1.2\jandex-3.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\glassfish\jaxb\jaxb-runtime\4.0.4\jaxb-runtime-4.0.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\glassfish\jaxb\jaxb-core\4.0.4\jaxb-core-4.0.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\glassfish\jaxb\txw2\4.0.4\txw2-4.0.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\antlr\antlr4-runtime\4.10.1\antlr4-runtime-4.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\data\spring-data-jpa\3.2.0\spring-data-jpa-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-orm\6.1.1\spring-orm-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-aspects\6.1.1\spring-aspects-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\mysql\mysql-connector-j\8.1.0\mysql-connector-j-8.1.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\h2database\h2\2.2.224\h2-2.2.224.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-thymeleaf\3.2.0\spring-boot-starter-thymeleaf-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\thymeleaf\thymeleaf-spring6\3.1.2.RELEASE\thymeleaf-spring6-3.1.2.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\thymeleaf\thymeleaf\3.1.2.RELEASE\thymeleaf-3.1.2.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\attoparser\attoparser\2.0.7.RELEASE\attoparser-2.0.7.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\oss\aliyun-sdk-oss\3.17.4\aliyun-sdk-oss-3.17.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\commons-codec\commons-codec\1.16.0\commons-codec-1.16.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\aliyun-java-sdk-core\4.5.10\aliyun-java-sdk-core-4.5.10.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="java.vm.vendor" value="Oracle Corporation"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://java.oracle.com/"/>
    <property name="user.timezone" value="Asia/Irkutsk"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="17"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="CN"/>
    <property name="sun.boot.library.path" value="D:\Program Files\Java_tools\jdk\jdk-17.0.12\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire5457496461739187342\surefirebooter-20250804005450064_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire5457496461739187342 2025-08-04T00-54-49_879-jvmRun1 surefire-20250804005450064_1tmp surefire_0-20250804005450064_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="test" value="VideoIdGenerationTest"/>
    <property name="surefire.test.class.path" value="D:\admin\desktop\video-player-project\target\test-classes;D:\admin\desktop\video-player-project\target\classes;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-web\3.2.0\spring-boot-starter-web-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter\3.2.0\spring-boot-starter-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot\3.2.0\spring-boot-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-autoconfigure\3.2.0\spring-boot-autoconfigure-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-logging\3.2.0\spring-boot-starter-logging-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\ch\qos\logback\logback-classic\1.4.11\logback-classic-1.4.11.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\ch\qos\logback\logback-core\1.4.11\logback-core-1.4.11.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\logging\log4j\log4j-to-slf4j\2.21.1\log4j-to-slf4j-2.21.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\logging\log4j\log4j-api\2.21.1\log4j-api-2.21.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\slf4j\jul-to-slf4j\2.0.9\jul-to-slf4j-2.0.9.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\annotation\jakarta.annotation-api\2.1.1\jakarta.annotation-api-2.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-json\3.2.0\spring-boot-starter-json-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\core\jackson-databind\2.15.3\jackson-databind-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\core\jackson-annotations\2.15.3\jackson-annotations-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\core\jackson-core\2.15.3\jackson-core-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\datatype\jackson-datatype-jdk8\2.15.3\jackson-datatype-jdk8-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\datatype\jackson-datatype-jsr310\2.15.3\jackson-datatype-jsr310-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\jackson\module\jackson-module-parameter-names\2.15.3\jackson-module-parameter-names-2.15.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-tomcat\3.2.0\spring-boot-starter-tomcat-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\tomcat\embed\tomcat-embed-core\10.1.16\tomcat-embed-core-10.1.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\tomcat\embed\tomcat-embed-websocket\10.1.16\tomcat-embed-websocket-10.1.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-web\6.1.1\spring-web-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-beans\6.1.1\spring-beans-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\micrometer\micrometer-observation\1.12.0\micrometer-observation-1.12.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\micrometer\micrometer-commons\1.12.0\micrometer-commons-1.12.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-webmvc\6.1.1\spring-webmvc-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-aop\6.1.1\spring-aop-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-context\6.1.1\spring-context-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-expression\6.1.1\spring-expression-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-data-jpa\3.2.0\spring-boot-starter-data-jpa-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-aop\3.2.0\spring-boot-starter-aop-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\aspectj\aspectjweaver\1.9.20.1\aspectjweaver-1.9.20.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-jdbc\3.2.0\spring-boot-starter-jdbc-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\zaxxer\HikariCP\5.0.1\HikariCP-5.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-jdbc\6.1.1\spring-jdbc-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hibernate\orm\hibernate-core\6.3.1.Final\hibernate-core-6.3.1.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\persistence\jakarta.persistence-api\3.1.0\jakarta.persistence-api-3.1.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\transaction\jakarta.transaction-api\2.0.1\jakarta.transaction-api-2.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\jboss\logging\jboss-logging\3.5.3.Final\jboss-logging-3.5.3.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hibernate\common\hibernate-commons-annotations\6.0.6.Final\hibernate-commons-annotations-6.0.6.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\smallrye\jandex\3.1.2\jandex-3.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\fasterxml\classmate\1.6.0\classmate-1.6.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\bytebuddy\byte-buddy\1.14.10\byte-buddy-1.14.10.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\glassfish\jaxb\jaxb-runtime\4.0.4\jaxb-runtime-4.0.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\glassfish\jaxb\jaxb-core\4.0.4\jaxb-core-4.0.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\eclipse\angus\angus-activation\2.0.1\angus-activation-2.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\glassfish\jaxb\txw2\4.0.4\txw2-4.0.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\sun\istack\istack-commons-runtime\4.1.2\istack-commons-runtime-4.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\inject\jakarta.inject-api\2.0.1\jakarta.inject-api-2.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\antlr\antlr4-runtime\4.10.1\antlr4-runtime-4.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\data\spring-data-jpa\3.2.0\spring-data-jpa-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\data\spring-data-commons\3.2.0\spring-data-commons-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-orm\6.1.1\spring-orm-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-tx\6.1.1\spring-tx-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-aspects\6.1.1\spring-aspects-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\mysql\mysql-connector-j\8.1.0\mysql-connector-j-8.1.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\h2database\h2\2.2.224\h2-2.2.224.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-thymeleaf\3.2.0\spring-boot-starter-thymeleaf-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\thymeleaf\thymeleaf-spring6\3.1.2.RELEASE\thymeleaf-spring6-3.1.2.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\thymeleaf\thymeleaf\3.1.2.RELEASE\thymeleaf-3.1.2.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\attoparser\attoparser\2.0.7.RELEASE\attoparser-2.0.7.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\unbescape\unbescape\1.1.6.RELEASE\unbescape-1.1.6.RELEASE.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-validation\3.2.0\spring-boot-starter-validation-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\tomcat\embed\tomcat-embed-el\10.1.16\tomcat-embed-el-10.1.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hibernate\validator\hibernate-validator\8.0.1.Final\hibernate-validator-8.0.1.Final.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\validation\jakarta.validation-api\3.0.2\jakarta.validation-api-3.0.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\oss\aliyun-sdk-oss\3.17.4\aliyun-sdk-oss-3.17.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\httpcomponents\httpclient\4.5.13\httpclient-4.5.13.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apache\httpcomponents\httpcore\4.4.16\httpcore-4.4.16.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\commons-logging\commons-logging\1.2\commons-logging-1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\commons-codec\commons-codec\1.16.0\commons-codec-1.16.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\jdom\jdom2\2.0.6.1\jdom2-2.0.6.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\codehaus\jettison\jettison\1.5.4\jettison-1.5.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\aliyun-java-sdk-core\4.5.10\aliyun-java-sdk-core-4.5.10.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\javax\xml\bind\jaxb-api\2.3.1\jaxb-api-2.3.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\javax\activation\javax.activation-api\1.2.0\javax.activation-api-1.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\jacoco\org.jacoco.agent\0.8.5\org.jacoco.agent-0.8.5-runtime.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\ini4j\ini4j\0.5.4\ini4j-0.5.4.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\opentracing\opentracing-api\0.33.0\opentracing-api-0.33.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\opentracing\opentracing-util\0.33.0\opentracing-util-0.33.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\io\opentracing\opentracing-noop\0.33.0\opentracing-noop-0.33.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\aliyun-java-sdk-ram\3.1.0\aliyun-java-sdk-ram-3.1.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\aliyun\aliyun-java-sdk-kms\2.11.0\aliyun-java-sdk-kms-2.11.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-starter-test\3.2.0\spring-boot-starter-test-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-test\3.2.0\spring-boot-test-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\boot\spring-boot-test-autoconfigure\3.2.0\spring-boot-test-autoconfigure-3.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\jayway\jsonpath\json-path\2.8.0\json-path-2.8.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\xml\bind\jakarta.xml.bind-api\4.0.1\jakarta.xml.bind-api-4.0.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\jakarta\activation\jakarta.activation-api\2.1.2\jakarta.activation-api-2.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\minidev\json-smart\2.5.0\json-smart-2.5.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\minidev\accessors-smart\2.5.0\accessors-smart-2.5.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\ow2\asm\asm\9.3\asm-9.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\assertj\assertj-core\3.24.2\assertj-core-3.24.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\awaitility\awaitility\4.2.0\awaitility-4.2.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\hamcrest\hamcrest\2.2\hamcrest-2.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter\5.10.1\junit-jupiter-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter-api\5.10.1\junit-jupiter-api-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\opentest4j\opentest4j\1.3.0\opentest4j-1.3.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\platform\junit-platform-commons\1.10.1\junit-platform-commons-1.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\apiguardian\apiguardian-api\1.1.2\apiguardian-api-1.1.2.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter-params\5.10.1\junit-jupiter-params-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\jupiter\junit-jupiter-engine\5.10.1\junit-jupiter-engine-5.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\junit\platform\junit-platform-engine\1.10.1\junit-platform-engine-1.10.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\mockito\mockito-core\5.7.0\mockito-core-5.7.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\net\bytebuddy\byte-buddy-agent\1.14.10\byte-buddy-agent-1.14.10.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\objenesis\objenesis\3.3\objenesis-3.3.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\mockito\mockito-junit-jupiter\5.7.0\mockito-junit-jupiter-5.7.0.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\skyscreamer\jsonassert\1.5.1\jsonassert-1.5.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\com\vaadin\external\google\android-json\0.0.20131108.vaadin1\android-json-0.0.20131108.vaadin1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-core\6.1.1\spring-core-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-jcl\6.1.1\spring-jcl-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\springframework\spring-test\6.1.1\spring-test-6.1.1.jar;D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository\org\xmlunit\xmlunit-core\2.9.1\xmlunit-core-2.9.1.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Java_tools\jdk\jdk-17.0.12"/>
    <property name="spring.profiles.active" value="test"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="D:\admin\desktop\video-player-project"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire5457496461739187342\surefirebooter-20250804005450064_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="17.0.12+8-LTS-286"/>
    <property name="user.name" value="adminn"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="Java(TM) SE Runtime Environment"/>
    <property name="file.encoding" value="GBK"/>
    <property name="java.vm.name" value="Java HotSpot(TM) 64-Bit Server VM"/>
    <property name="localRepository" value="D:\Program Files\maven\apache-maven-3.9.1\apache-maven-3.9.1-repository"/>
    <property name="java.vendor.url.bug" value="https://bugreport.java.com/bugreport/"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="17.0.12"/>
    <property name="user.dir" value="D:\admin\desktop\video-player-project"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="GBK"/>
    <property name="java.library.path" value="D:\Program Files\Java_tools\jdk\jdk-17.0.12\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;c:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;d:\Program Files (x86)\cursor\resources\app\bin;C:\Windows\system32;C:\Windows;C:\Windows\System32\Wbem;C:\Windows\System32\WindowsPowerShell\v1.0\;C:\Windows\System32\OpenSSH\;D:\Program Files\微信web开发者工具\dll;C:\Use;s\adminn\AppData\Local\Microsoft\WindowsApps;D:\Program Files (x86)\cursor\resources\app\bin;D:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\nodejs\node_global\node_modules;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\nodejs\node_global;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Program Files\Java_tools\jdk\jdk-17.0.12\jre\bin;D:\Program Files\Java_tools\jdk\jdk-17.0.12\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;D:\Program Files\Java_tools\mysql\mysql-8.1.0-winx64\basedir\mysql-8.1.0-winx64\bin\;D:\Program Files\Java_tools\maven\apache-maven-3.9.1\apache-maven-3.9.1\bin;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Program Files\nodejs\node_global;C:\Users\<USER>\AppData\Local\Programs\cursor\resources\app\bin;D:\Program Files\Microsoft VS Code\bin;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="java.vendor" value="Oracle Corporation"/>
    <property name="java.vm.version" value="17.0.12+8-LTS-286"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="61.0"/>
  </properties>
  <testcase name="testVideoIdGeneration" classname="com.videoplayer.VideoIdGenerationTest" time="0.108">
    <system-out><![CDATA[生成的视频ID: tJPesWXD2wmwL2Qn7o
]]></system-out>
  </testcase>
  <testcase name="testVideoIdUniqueness" classname="com.videoplayer.VideoIdGenerationTest" time="0.106">
    <system-out><![CDATA[成功生成 1000 个唯一的视频ID
示例ID: ahfa2nja66cDhl8Aco
]]></system-out>
  </testcase>
  <testcase name="testVideoWithTitleAndUrl" classname="com.videoplayer.VideoIdGenerationTest" time="0.002">
    <system-out><![CDATA[带参数构造的视频ID: Hjn4J0RkPPFavRrhcz
]]></system-out>
  </testcase>
  <testcase name="testIdCharacterDistribution" classname="com.videoplayer.VideoIdGenerationTest" time="0.016">
    <system-out><![CDATA[在 100 个ID中使用了 62 种不同字符
使用的字符示例: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F, G
]]></system-out>
  </testcase>
</testsuite>