<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试批量状态更新</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">调试批量状态更新</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>视频列表</h3>
                <div id="videoList"></div>
                <button class="btn btn-primary mt-3" onclick="loadVideos()">刷新列表</button>
            </div>
            <div class="col-md-6">
                <h3>调试信息</h3>
                <div id="debugInfo" class="border p-3" style="height: 400px; overflow-y: auto; background-color: #f8f9fa;"></div>
                <button class="btn btn-danger mt-2" onclick="clearDebug()">清空日志</button>
            </div>
        </div>
    </div>

    <script>
        let allVideos = [];

        function log(message, type = 'info') {
            const debugDiv = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            debugDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            debugDiv.scrollTop = debugDiv.scrollHeight;
        }

        function clearDebug() {
            document.getElementById('debugInfo').innerHTML = '';
        }

        async function loadVideos() {
            log('开始加载视频列表...');
            
            try {
                const response = await fetch('/api/videos?page=0&size=10');
                log(`API响应状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`API响应数据: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success && result.data) {
                    allVideos = result.data;
                    displayVideos(result.data);
                    log(`成功加载 ${result.data.length} 个视频`, 'success');
                } else {
                    log(`加载失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                log(`网络错误: ${error.message}`, 'error');
            }
        }

        function displayVideos(videos) {
            const container = document.getElementById('videoList');
            
            container.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>标题</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${videos.map(video => `
                                <tr>
                                    <td>${video.id}</td>
                                    <td>${video.title.substring(0, 20)}...</td>
                                    <td>
                                        <span class="badge ${video.isActive ? 'bg-success' : 'bg-danger'}">
                                            ${video.isActive ? '启用' : '禁用'}
                                        </span>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-warning me-1" 
                                                onclick="testDisable(${video.id})">
                                            禁用
                                        </button>
                                        <button class="btn btn-sm btn-success" 
                                                onclick="testEnable(${video.id})">
                                            启用
                                        </button>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        async function testDisable(videoId) {
            log(`开始禁用视频 ${videoId}...`);
            
            try {
                // 先获取当前视频信息
                const getResponse = await fetch(`/api/videos/${videoId}`);
                const getCurrentResult = await getResponse.json();
                log(`获取视频 ${videoId} 当前信息: ${JSON.stringify(getCurrentResult, null, 2)}`);
                
                // 发送禁用请求
                const updateData = { isActive: false };
                log(`发送禁用请求数据: ${JSON.stringify(updateData)}`);
                
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'PUT',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
                
                log(`禁用请求响应状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`禁用请求响应数据: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    log(`视频 ${videoId} 禁用成功！`, 'success');
                    
                    // 验证更新结果
                    if (result.data && result.data.isActive !== undefined) {
                        log(`更新后的状态: isActive = ${result.data.isActive}`, result.data.isActive ? 'warning' : 'success');
                    }
                    
                    // 重新加载列表
                    setTimeout(loadVideos, 1000);
                } else {
                    log(`禁用失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                log(`禁用视频 ${videoId} 时发生错误: ${error.message}`, 'error');
            }
        }

        async function testEnable(videoId) {
            log(`开始启用视频 ${videoId}...`);
            
            try {
                // 先获取当前视频信息
                const getResponse = await fetch(`/api/videos/${videoId}`);
                const getCurrentResult = await getResponse.json();
                log(`获取视频 ${videoId} 当前信息: ${JSON.stringify(getCurrentResult, null, 2)}`);
                
                // 发送启用请求
                const updateData = { isActive: true };
                log(`发送启用请求数据: ${JSON.stringify(updateData)}`);
                
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'PUT',
                    headers: { 
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(updateData)
                });
                
                log(`启用请求响应状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`启用请求响应数据: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    log(`视频 ${videoId} 启用成功！`, 'success');
                    
                    // 验证更新结果
                    if (result.data && result.data.isActive !== undefined) {
                        log(`更新后的状态: isActive = ${result.data.isActive}`, result.data.isActive ? 'success' : 'warning');
                    }
                    
                    // 重新加载列表
                    setTimeout(loadVideos, 1000);
                } else {
                    log(`启用失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                log(`启用视频 ${videoId} 时发生错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动获取视频列表
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，开始初始化...');
            loadVideos();
        });
    </script>
</body>
</html>
