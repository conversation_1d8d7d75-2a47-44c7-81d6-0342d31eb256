<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端视频限制测试</title>
    <style>
        /* 复制关键的CSS样式 */
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 1.5rem;
            margin: 0;
            padding: 20px;
        }

        .video-card {
            border: 1px solid #ddd;
            border-radius: 12px;
            padding: 15px;
            background: #f9f9f9;
            text-align: center;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .video-grid {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 1rem;
            }
            
            /* 手机端只显示前两个视频 */
            .video-grid .video-card:nth-child(n+3) {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .video-grid {
                grid-template-columns: 1fr;
                gap: 0.75rem;
            }
            
            /* 小屏手机端只显示前两个视频 */
            .video-grid .video-card:nth-child(n+3) {
                display: none;
            }
        }

        .test-info {
            background: #e3f2fd;
            padding: 15px;
            margin: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }

        .screen-size {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="screen-size" id="screenSize"></div>
    
    <div class="test-info">
        <h2>手机端视频限制测试</h2>
        <p><strong>测试说明：</strong></p>
        <ul>
            <li>桌面端（>768px）：显示所有6个视频</li>
            <li>平板端（≤768px）：只显示前2个视频</li>
            <li>手机端（≤480px）：只显示前2个视频</li>
        </ul>
        <p><strong>操作：</strong>调整浏览器窗口大小或使用开发者工具的设备模拟器来测试不同屏幕尺寸。</p>
    </div>

    <div class="video-grid">
        <div class="video-card">
            <h3>视频 1</h3>
            <p>这是第一个视频</p>
        </div>
        <div class="video-card">
            <h3>视频 2</h3>
            <p>这是第二个视频</p>
        </div>
        <div class="video-card">
            <h3>视频 3</h3>
            <p>这是第三个视频（手机端隐藏）</p>
        </div>
        <div class="video-card">
            <h3>视频 4</h3>
            <p>这是第四个视频（手机端隐藏）</p>
        </div>
        <div class="video-card">
            <h3>视频 5</h3>
            <p>这是第五个视频（手机端隐藏）</p>
        </div>
        <div class="video-card">
            <h3>视频 6</h3>
            <p>这是第六个视频（手机端隐藏）</p>
        </div>
    </div>

    <script>
        function updateScreenSize() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const screenSize = document.getElementById('screenSize');
            
            let deviceType = '';
            if (width > 768) {
                deviceType = '桌面端';
            } else if (width > 480) {
                deviceType = '平板端';
            } else {
                deviceType = '手机端';
            }
            
            screenSize.textContent = `${width}×${height} (${deviceType})`;
        }

        window.addEventListener('resize', updateScreenSize);
        updateScreenSize();
    </script>
</body>
</html>
