package com.videoplayer.controller;

import com.videoplayer.common.ApiResponse;
import com.videoplayer.entity.Video;
import com.videoplayer.exception.ResourceNotFoundException;
import com.videoplayer.service.VideoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Optional;

/**
 * 视频控制器
 */
@RestController
@RequestMapping("/api/videos")
@CrossOrigin(originPatterns = "*", allowCredentials = "true")
public class VideoApi {

    private static final Logger logger = LoggerFactory.getLogger(VideoApi.class);

    @Autowired
    private VideoService videoService;

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public ResponseEntity<ApiResponse<String>> health() {
        try {
            long count = videoService.getTotalVideoCount();
            return ResponseEntity.ok(ApiResponse.success("系统正常，视频总数: " + count));
        } catch (Exception e) {
            logger.error("健康检查失败", e);
            return ResponseEntity.status(500).body(ApiResponse.error("HEALTH_CHECK_FAILED", "系统异常: " + e.getMessage()));
        }
    }

    /**
     * 获取所有视频列表
     */
    @GetMapping
    public ResponseEntity<ApiResponse<List<Video>>> getAllVideos(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdTime") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {

        try {
            logger.info("获取视频列表 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir);

            Page<Video> videoPage = videoService.getAllActiveVideos(page, size, sortBy, sortDir);

            return ResponseEntity.ok(ApiResponse.success(
                videoPage.getContent(),
                videoPage.getTotalElements(),
                page,
                size
            ));
        } catch (Exception e) {
            logger.error("获取视频列表失败 - page: {}, size: {}, sortBy: {}, sortDir: {}", page, size, sortBy, sortDir, e);
            throw e; // 重新抛出异常，让GlobalExceptionHandler处理
        }
    }

    /**
     * 根据ID获取视频详情
     */
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Video>> getVideoById(@PathVariable Long id) {
        logger.info("获取视频详情 - id: {}", id);
        
        Optional<Video> optionalVideo = videoService.getVideoById(id);
        
        if (optionalVideo.isEmpty()) {
            throw new ResourceNotFoundException("Video", id);
        }
        
        return ResponseEntity.ok(ApiResponse.success("获取视频详情成功", optionalVideo.get()));
    }

    /**
     * 搜索视频
     */
    @GetMapping("/search")
    public ResponseEntity<ApiResponse<List<Video>>> searchVideos(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {
        
        logger.info("搜索视频 - keyword: {}, page: {}, size: {}", keyword, page, size);
        
        Page<Video> videoPage = videoService.searchVideos(keyword, page, size);
        
        return ResponseEntity.ok(ApiResponse.success(
            videoPage.getContent(), 
            videoPage.getTotalElements(), 
            page, 
            size
        ));
    }

    /**
     * 添加视频
     */
    @PostMapping
    public ResponseEntity<ApiResponse<Video>> addVideo(@Valid @RequestBody Video video) {
        logger.info("添加视频 - title: {}", video.getTitle());

        Video savedVideo = videoService.saveVideo(video);

        return ResponseEntity.ok(ApiResponse.success("添加视频成功", savedVideo));
    }

    /**
     * 更新视频
     */
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Video>> updateVideo(@PathVariable Long id, @RequestBody Video video) {
        logger.info("更新视频 - id: {}", id);
        logger.info("接收到的视频数据: {}", video);
        logger.info("接收到的isActive值: {}", video.getIsActive());

        Video updatedVideo = videoService.updateVideo(id, video);
        if (updatedVideo == null) {
            throw new ResourceNotFoundException("Video", id);
        }

        logger.info("更新后的视频数据: {}", updatedVideo);
        logger.info("更新后的isActive值: {}", updatedVideo.getIsActive());
        return ResponseEntity.ok(ApiResponse.success("更新视频成功", updatedVideo));
    }

    /**
     * 删除视频 - 只能删除已禁用的视频（永久删除）
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<String>> deleteVideo(@PathVariable Long id) {
        logger.info("永久删除视频 - id: {}", id);

        try {
            // 使用强制删除方法确保真正的永久删除
            boolean deleted = videoService.forceDeleteVideoById(id);
            if (!deleted) {
                throw new ResourceNotFoundException("Video", id);
            }

            logger.info("视频永久删除成功 - id: {}", id);
            return ResponseEntity.ok(ApiResponse.success("视频已永久删除"));
        } catch (IllegalStateException e) {
            logger.warn("删除视频失败 - id: {}, 原因: {}", id, e.getMessage());
            return ResponseEntity.badRequest().body(ApiResponse.error("DELETE_FAILED", e.getMessage()));
        } catch (Exception e) {
            logger.error("删除视频时发生错误 - id: {}, 错误: {}", id, e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("DELETE_ERROR", "删除操作失败：" + e.getMessage()));
        }
    }

    /**
     * 切换视频置顶状态
     */
    @PutMapping("/{id}/pin")
    public ResponseEntity<ApiResponse<String>> toggleVideoPinStatus(@PathVariable Long id) {
        logger.info("切换视频置顶状态 - id: {}", id);

        try {
            boolean success = videoService.toggleVideoPinStatus(id);
            if (!success) {
                throw new ResourceNotFoundException("Video", id);
            }

            logger.info("视频置顶状态切换成功 - id: {}", id);
            return ResponseEntity.ok(ApiResponse.success("置顶状态切换成功"));
        } catch (Exception e) {
            logger.error("切换视频置顶状态时发生错误 - id: {}, 错误: {}", id, e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("PIN_ERROR", "置顶操作失败：" + e.getMessage()));
        }
    }

    /**
     * 设置视频置顶状态
     */
    @PutMapping("/{id}/pin/{status}")
    public ResponseEntity<ApiResponse<String>> setVideoPinStatus(@PathVariable Long id, @PathVariable boolean status) {
        logger.info("设置视频置顶状态 - id: {}, status: {}", id, status);

        try {
            boolean success = videoService.setVideoPinStatus(id, status);
            if (!success) {
                throw new ResourceNotFoundException("Video", id);
            }

            String message = status ? "视频已置顶到首页" : "视频已取消置顶";
            logger.info("视频置顶状态设置成功 - id: {}, status: {}", id, status);
            return ResponseEntity.ok(ApiResponse.success(message));
        } catch (Exception e) {
            logger.error("设置视频置顶状态时发生错误 - id: {}, status: {}, 错误: {}", id, status, e.getMessage(), e);
            return ResponseEntity.status(500).body(ApiResponse.error("PIN_ERROR", "置顶操作失败：" + e.getMessage()));
        }
    }
}
