<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Thymeleaf条件测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        .condition-test {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .result-true {
            background: #d4edda;
            color: #155724;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px;
        }
        
        .result-false {
            background: #f8d7da;
            color: #721c24;
            padding: 8px 12px;
            border-radius: 4px;
            display: inline-block;
            margin: 5px;
        }
        
        .code-inline {
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">Thymeleaf条件判断测试</h1>
        
        <!-- 条件判断说明 -->
        <div class="test-section">
            <h3 class="test-title">Thymeleaf条件判断差异</h3>
            <p>在Thymeleaf中，不同的条件表达式可能产生不同的结果，特别是在处理布尔值时：</p>
            
            <div class="condition-test">
                <h5>1. 简单条件判断</h5>
                <p><code class="code-inline">${hasNextPage}</code></p>
                <ul>
                    <li>当 hasNextPage = true 时：<span class="result-true">条件为真</span></li>
                    <li>当 hasNextPage = false 时：<span class="result-false">条件为假</span></li>
                    <li>当 hasNextPage = null 时：<span class="result-false">条件为假</span></li>
                </ul>
                <small class="text-muted">问题：在某些边界情况下可能不够精确</small>
            </div>
            
            <div class="condition-test">
                <h5>2. 明确的布尔比较（推荐）</h5>
                <p><code class="code-inline">${hasNextPage == true}</code></p>
                <ul>
                    <li>当 hasNextPage = true 时：<span class="result-true">条件为真</span></li>
                    <li>当 hasNextPage = false 时：<span class="result-false">条件为假</span></li>
                    <li>当 hasNextPage = null 时：<span class="result-false">条件为假</span></li>
                </ul>
                <small class="text-success">优势：明确的布尔值比较，更加可靠</small>
            </div>
            
            <div class="condition-test">
                <h5>3. 处理null情况</h5>
                <p><code class="code-inline">${hasNextPage == false or hasNextPage == null}</code></p>
                <ul>
                    <li>当 hasNextPage = true 时：<span class="result-false">条件为假</span></li>
                    <li>当 hasNextPage = false 时：<span class="result-true">条件为真</span></li>
                    <li>当 hasNextPage = null 时：<span class="result-true">条件为真</span></li>
                </ul>
                <small class="text-success">优势：明确处理null值，避免意外情况</small>
            </div>
        </div>
        
        <!-- 修复前后对比 -->
        <div class="test-section">
            <h3 class="test-title">修复前后对比</h3>
            
            <div class="row">
                <div class="col-md-6">
                    <h5 class="text-danger">修复前</h5>
                    <div class="condition-test">
                        <pre><code>&lt;a th:if="${hasNextPage}" 
   class="page-link" 
   th:href="@{/videos(page=${currentPage + 1})}"&gt;
    下一页
&lt;/a&gt;</code></pre>
                        <p><strong>问题：</strong></p>
                        <ul>
                            <li>可能在边界情况下判断不准确</li>
                            <li>如果hasNextPage为null，可能产生意外行为</li>
                            <li>某些情况下仍然会生成链接</li>
                        </ul>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <h5 class="text-success">修复后</h5>
                    <div class="condition-test">
                        <pre><code>&lt;a th:if="${hasNextPage == true}" 
   class="page-link" 
   th:href="@{/videos(page=${currentPage + 1})}"&gt;
    下一页
&lt;/a&gt;
&lt;span th:if="${hasNextPage == false or hasNextPage == null}" 
      class="page-link"&gt;
    下一页
&lt;/span&gt;</code></pre>
                        <p><strong>改进：</strong></p>
                        <ul>
                            <li>明确的布尔值比较</li>
                            <li>处理null值情况</li>
                            <li>确保只有在明确为true时才生成链接</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试场景 -->
        <div class="test-section">
            <h3 class="test-title">测试场景模拟</h3>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 模拟不同的hasNextPage值</h5>
                <p>以下是不同hasNextPage值下的预期行为：</p>
            </div>
            
            <div class="row">
                <div class="col-md-4">
                    <div class="condition-test">
                        <h6>hasNextPage = true</h6>
                        <p>✅ 显示可点击的下一页链接</p>
                        <p>✅ 链接指向下一页</p>
                        <p>✅ 按钮样式正常</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="condition-test">
                        <h6>hasNextPage = false</h6>
                        <p>🚫 显示禁用的下一页span</p>
                        <p>🚫 不可点击</p>
                        <p>🚫 禁用样式（白色背景）</p>
                    </div>
                </div>
                
                <div class="col-md-4">
                    <div class="condition-test">
                        <h6>hasNextPage = null</h6>
                        <p>🚫 显示禁用的下一页span</p>
                        <p>🚫 不可点击</p>
                        <p>🚫 禁用样式（白色背景）</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 验证清单 -->
        <div class="test-section">
            <h3 class="test-title">验证清单</h3>
            <div class="alert alert-warning">
                <h5><i class="fas fa-clipboard-check"></i> 测试清单</h5>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check1">
                    <label class="form-check-label" for="check1">
                        启动项目并访问 /videos 页面
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check2">
                    <label class="form-check-label" for="check2">
                        导航到最后一页
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check3">
                    <label class="form-check-label" for="check3">
                        确认下一页按钮显示为禁用状态（白色背景）
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check4">
                    <label class="form-check-label" for="check4">
                        点击下一页按钮，确认没有跳转
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check5">
                    <label class="form-check-label" for="check5">
                        检查浏览器控制台，确认没有错误
                    </label>
                </div>
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" id="check6">
                    <label class="form-check-label" for="check6">
                        检查服务器日志，查看hasNextPage的调试信息
                    </label>
                </div>
            </div>
        </div>
        
        <!-- 总结 -->
        <div class="test-section">
            <h3 class="test-title">修复总结</h3>
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle"></i> 修复完成</h5>
                <ul class="mb-0">
                    <li>✅ 使用明确的布尔值比较 <code>${hasNextPage == true}</code></li>
                    <li>✅ 处理null值情况 <code>${hasNextPage == false or hasNextPage == null}</code></li>
                    <li>✅ 添加调试日志便于问题排查</li>
                    <li>✅ 确保禁用状态下完全不可点击</li>
                    <li>✅ 保持CSS样式的一致性</li>
                </ul>
            </div>
            
            <div class="alert alert-info">
                <h5><i class="fas fa-lightbulb"></i> 最佳实践</h5>
                <p class="mb-0">
                    在Thymeleaf中处理布尔值时，建议使用明确的比较操作符（==、!=）而不是依赖隐式的真假判断，
                    这样可以避免边界情况下的意外行为，提高代码的可靠性。
                </p>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
