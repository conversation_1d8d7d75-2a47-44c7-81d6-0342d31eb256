-- 测试置顶功能的数据库操作脚本
USE video_player;

-- 1. 查看当前所有视频的置顶状态
SELECT 
    id,
    title,
    is_active,
    is_pinned,
    created_time
FROM videos 
ORDER BY id;

-- 2. 手动设置视频1为置顶（用于测试）
UPDATE videos SET is_pinned = 1 WHERE id = 1;

-- 3. 验证更新结果
SELECT 
    id,
    title,
    is_active,
    is_pinned,
    updated_time
FROM videos 
WHERE id = 1;

-- 4. 查看置顶视频的排序效果
SELECT 
    id,
    title,
    is_active,
    is_pinned,
    created_time
FROM videos 
ORDER BY is_pinned DESC, created_time DESC;

-- 5. 取消置顶（用于测试切换功能）
UPDATE videos SET is_pinned = 0 WHERE id = 1;

-- 6. 再次验证
SELECT 
    id,
    title,
    is_active,
    is_pinned,
    updated_time
FROM videos 
WHERE id = 1;

-- 7. 统计置顶视频数量
SELECT 
    COUNT(*) as total_videos,
    SUM(CASE WHEN is_pinned = 1 THEN 1 ELSE 0 END) as pinned_count,
    SUM(CASE WHEN is_pinned = 0 OR is_pinned IS NULL THEN 1 ELSE 0 END) as unpinned_count
FROM videos;
