<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频随机ID生成演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .id-display {
            font-family: 'Courier New', monospace;
            font-size: 1.2em;
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #dee2e6;
        }
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .char-analysis {
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">🎬 视频随机ID生成演示</h1>
                <p class="text-center text-muted">模拟Java后端的18位随机ID生成逻辑</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-8 mx-auto">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🎲 ID生成器</h5>
                    </div>
                    <div class="card-body text-center">
                        <div class="id-display mb-3" id="currentId">点击按钮生成ID</div>
                        <button class="btn btn-primary btn-lg" onclick="generateNewId()">
                            <i class="fas fa-refresh"></i> 生成新ID
                        </button>
                        <button class="btn btn-success ms-2" onclick="generateBatch()">
                            <i class="fas fa-list"></i> 批量生成
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 id="totalGenerated">0</h3>
                        <p class="mb-0">已生成ID数量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 id="uniqueCount">0</h3>
                        <p class="mb-0">唯一ID数量</p>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <h3 id="collisionRate">0%</h3>
                        <p class="mb-0">碰撞率</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📊 字符分析</h5>
                    </div>
                    <div class="card-body char-analysis">
                        <div class="row">
                            <div class="col-4">
                                <strong>大写字母:</strong><br>
                                <span id="uppercaseCount">0</span> / 26
                            </div>
                            <div class="col-4">
                                <strong>小写字母:</strong><br>
                                <span id="lowercaseCount">0</span> / 26
                            </div>
                            <div class="col-4">
                                <strong>数字:</strong><br>
                                <span id="digitCount">0</span> / 10
                            </div>
                        </div>
                        <div class="mt-3">
                            <strong>使用的字符:</strong><br>
                            <div id="usedChars" class="text-muted">暂无数据</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">📝 最近生成的ID</h5>
                    </div>
                    <div class="card-body">
                        <div id="recentIds" style="max-height: 200px; overflow-y: auto;">
                            <p class="text-muted">暂无数据</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">ℹ️ 技术说明</h5>
                    </div>
                    <div class="card-body">
                        <ul>
                            <li><strong>ID长度:</strong> 固定18位</li>
                            <li><strong>字符集:</strong> A-Z, a-z, 0-9 (共62种字符)</li>
                            <li><strong>理论组合数:</strong> 62^18 ≈ 1.3 × 10^32</li>
                            <li><strong>安全性:</strong> 使用加密安全的随机数生成器</li>
                            <li><strong>碰撞概率:</strong> 在生成10^9个ID的情况下，碰撞概率约为10^-14</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟Java后端的随机ID生成逻辑
        const CHARACTERS = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        const ID_LENGTH = 18;
        
        let generatedIds = new Set();
        let totalGenerated = 0;
        let recentIds = [];
        let usedChars = new Set();

        function generateRandomId() {
            let result = '';
            for (let i = 0; i < ID_LENGTH; i++) {
                const randomIndex = Math.floor(Math.random() * CHARACTERS.length);
                result += CHARACTERS.charAt(randomIndex);
            }
            return result;
        }

        function generateNewId() {
            const newId = generateRandomId();
            document.getElementById('currentId').textContent = newId;
            
            // 更新统计
            totalGenerated++;
            generatedIds.add(newId);
            recentIds.unshift(newId);
            if (recentIds.length > 10) {
                recentIds.pop();
            }

            // 分析字符
            for (let char of newId) {
                usedChars.add(char);
            }

            updateStats();
            updateRecentIds();
            updateCharAnalysis();
        }

        function generateBatch() {
            for (let i = 0; i < 100; i++) {
                const newId = generateRandomId();
                totalGenerated++;
                generatedIds.add(newId);
                
                for (let char of newId) {
                    usedChars.add(char);
                }
            }
            
            updateStats();
            updateCharAnalysis();
            
            // 显示最后一个生成的ID
            const lastId = generateRandomId();
            document.getElementById('currentId').textContent = lastId + ' (批量生成完成)';
        }

        function updateStats() {
            document.getElementById('totalGenerated').textContent = totalGenerated.toLocaleString();
            document.getElementById('uniqueCount').textContent = generatedIds.size.toLocaleString();
            
            const collisionRate = totalGenerated > 0 ? 
                ((totalGenerated - generatedIds.size) / totalGenerated * 100).toFixed(6) : 0;
            document.getElementById('collisionRate').textContent = collisionRate + '%';
        }

        function updateRecentIds() {
            const container = document.getElementById('recentIds');
            if (recentIds.length === 0) {
                container.innerHTML = '<p class="text-muted">暂无数据</p>';
                return;
            }
            
            container.innerHTML = recentIds.map(id => 
                `<div class="id-display mb-2">${id}</div>`
            ).join('');
        }

        function updateCharAnalysis() {
            const uppercase = Array.from(usedChars).filter(c => c >= 'A' && c <= 'Z').length;
            const lowercase = Array.from(usedChars).filter(c => c >= 'a' && c <= 'z').length;
            const digits = Array.from(usedChars).filter(c => c >= '0' && c <= '9').length;
            
            document.getElementById('uppercaseCount').textContent = uppercase;
            document.getElementById('lowercaseCount').textContent = lowercase;
            document.getElementById('digitCount').textContent = digits;
            
            const sortedChars = Array.from(usedChars).sort();
            document.getElementById('usedChars').textContent = 
                sortedChars.length > 0 ? sortedChars.join(' ') : '暂无数据';
        }

        // 页面加载时生成第一个ID
        window.onload = function() {
            generateNewId();
        };
    </script>
</body>
</html>
