<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>置顶点击功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .status-demo {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            background: #f8f9fa;
        }
        .clickable-pin-status {
            cursor: pointer;
            transition: all 0.2s ease;
        }
        .clickable-pin-status:hover {
            transform: scale(1.05);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .log-area {
            background: #f1f1f1;
            border: 1px solid #ccc;
            border-radius: 5px;
            padding: 10px;
            height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-mouse-pointer text-primary me-2"></i>
            置顶点击功能测试
        </h1>

        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>测试说明</h5>
            <p>此页面用于测试置顶标识的点击功能。点击下方的置顶标识来切换状态。</p>
            <p><strong>应用地址</strong>: http://localhost:56479</p>
        </div>

        <!-- 状态演示区域 -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5><i class="fas fa-desktop me-2"></i>状态演示</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="status-demo text-center">
                            <h6>视频1 - 佳茵轻康</h6>
                            <div class="d-flex flex-column align-items-center gap-1">
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>启用
                                </span>
                                <span id="pin-badge-1" class="badge bg-warning text-dark clickable-pin-status" 
                                      onclick="testTogglePin(1, true)"
                                      title="点击取消置顶">
                                    <i class="fas fa-thumbtack me-1"></i>置顶
                                </span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="status-demo text-center">
                            <h6>视频17 - 测试01</h6>
                            <div class="d-flex flex-column align-items-center gap-1">
                                <span class="badge bg-success">
                                    <i class="fas fa-check me-1"></i>启用
                                </span>
                                <span id="pin-badge-17" class="badge bg-light text-dark border clickable-pin-status" 
                                      onclick="testTogglePin(17, false)"
                                      title="点击设置为置顶">
                                    <i class="far fa-thumbtack me-1"></i>未置顶
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- API测试区域 -->
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5><i class="fas fa-cog me-2"></i>API测试</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <input type="number" id="testVideoId" class="form-control" placeholder="视频ID" value="1">
                    </div>
                    <div class="col-md-8">
                        <button class="btn btn-primary me-2" onclick="apiTogglePin()">
                            <i class="fas fa-sync me-1"></i>切换置顶状态
                        </button>
                        <button class="btn btn-warning me-2" onclick="apiSetPin(true)">
                            <i class="fas fa-thumbtack me-1"></i>设置置顶
                        </button>
                        <button class="btn btn-secondary" onclick="apiSetPin(false)">
                            <i class="far fa-thumbtack me-1"></i>取消置顶
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 日志区域 -->
        <div class="card">
            <div class="card-header bg-dark text-white">
                <h5><i class="fas fa-terminal me-2"></i>操作日志</h5>
            </div>
            <div class="card-body">
                <div id="logArea" class="log-area"></div>
                <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearLog()">
                    <i class="fas fa-trash me-1"></i>清空日志
                </button>
            </div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:56479/api';
        
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'color: red;' : type === 'success' ? 'color: green;' : 'color: blue;';
            logArea.innerHTML += `<div style="${color}">[${time}] ${message}</div>`;
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logArea').innerHTML = '';
        }
        
        // 测试点击置顶标识
        async function testTogglePin(videoId, currentStatus) {
            try {
                log(`点击视频 ${videoId} 的置顶标识，当前状态: ${currentStatus ? '已置顶' : '未置顶'}`);
                
                const response = await fetch(`${API_BASE}/videos/${videoId}/pin`, {
                    method: 'PUT'
                });
                
                const result = await response.json();
                log(`API响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    log(`✅ ${result.message}`, 'success');
                    updateDemoBadge(videoId, !currentStatus);
                } else {
                    log(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 更新演示区域的徽章
        function updateDemoBadge(videoId, isPinned) {
            const badge = document.getElementById(`pin-badge-${videoId}`);
            if (badge) {
                if (isPinned) {
                    badge.className = 'badge bg-warning text-dark clickable-pin-status';
                    badge.innerHTML = '<i class="fas fa-thumbtack me-1"></i>置顶';
                    badge.title = '点击取消置顶';
                    badge.onclick = () => testTogglePin(videoId, true);
                } else {
                    badge.className = 'badge bg-light text-dark border clickable-pin-status';
                    badge.innerHTML = '<i class="far fa-thumbtack me-1"></i>未置顶';
                    badge.title = '点击设置为置顶';
                    badge.onclick = () => testTogglePin(videoId, false);
                }
            }
        }
        
        // API测试函数
        async function apiTogglePin() {
            const videoId = document.getElementById('testVideoId').value;
            if (!videoId) {
                log('请输入视频ID', 'error');
                return;
            }
            await testTogglePin(parseInt(videoId), true); // 假设当前是置顶状态
        }
        
        async function apiSetPin(status) {
            const videoId = document.getElementById('testVideoId').value;
            if (!videoId) {
                log('请输入视频ID', 'error');
                return;
            }
            
            try {
                log(`设置视频 ${videoId} 置顶状态为: ${status}`);
                
                const response = await fetch(`${API_BASE}/videos/${videoId}/pin/${status}`, {
                    method: 'PUT'
                });
                
                const result = await response.json();
                log(`API响应: ${JSON.stringify(result)}`);
                
                if (result.success) {
                    log(`✅ ${result.message}`, 'success');
                } else {
                    log(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时初始化
        window.onload = function() {
            log('🎬 置顶点击功能测试页面已加载');
            log('💡 点击上方的置顶标识来测试功能');
            log('🔗 管理页面: http://localhost:56479/admin');
        };
    </script>
</body>
</html>
