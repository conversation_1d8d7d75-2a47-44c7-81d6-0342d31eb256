# 视频置顶功能说明

## 🎯 功能概述

视频置顶功能允许管理员将重要的视频置顶到首页，置顶的视频会优先显示在视频列表的最前面，提升重要内容的曝光度。

## ✨ 功能特点

- **灵活置顶**: 支持任意视频的置顶和取消置顶
- **可视化管理**: 在管理后台直观显示置顶状态
- **智能排序**: 置顶视频优先显示，同级别按时间排序
- **状态标识**: 置顶视频带有明显的视觉标识

## 🔧 使用方法

### 1. 在管理后台置顶视频

1. 访问管理页面：`/admin`
2. 在视频列表中找到要置顶的视频
3. 点击操作列中的 📌 按钮
4. 置顶成功后，视频标题旁会显示"置顶"标识
5. 再次点击可取消置顶

### 2. 置顶状态标识

- **置顶视频**: 显示黄色"置顶"标识，按钮为实心图钉
- **普通视频**: 无特殊标识，按钮为空心图钉

### 3. 排序规则

- 置顶视频始终排在普通视频前面
- 多个置顶视频之间按创建时间倒序排列
- 普通视频按创建时间倒序排列

## 🛠️ 技术实现

### 数据库变更

```sql
-- 添加置顶字段
ALTER TABLE `videos` ADD COLUMN `is_pinned` BOOLEAN DEFAULT FALSE COMMENT '是否置顶到首页';

-- 添加索引
ALTER TABLE `videos` ADD INDEX `idx_is_pinned` (`is_pinned`);
```

### API接口

```http
# 切换置顶状态
PUT /api/videos/{id}/pin

# 设置置顶状态
PUT /api/videos/{id}/pin/{status}
```

### 前端功能

- 置顶按钮：点击切换置顶状态
- 状态标识：显示当前置顶状态
- 实时更新：操作后立即更新界面

## 📱 界面展示

### 管理后台

- **置顶按钮**: 位于操作列，图钉图标
- **状态标识**: 视频标题旁的黄色"置顶"标签
- **视觉反馈**: 按钮状态实时更新

### 首页展示

- 置顶视频自动排在列表最前面
- 保持原有的视觉样式
- 用户无感知的优先级调整

## 🔄 数据迁移

如果是现有项目升级，请执行以下步骤：

1. **备份数据库**
   ```bash
   mysqldump -u root -p video_player > backup_$(date +%Y%m%d).sql
   ```

2. **执行迁移脚本**
   ```bash
   mysql -u root -p video_player < database/migration_add_is_pinned.sql
   ```

3. **重启应用**
   ```bash
   mvn spring-boot:run
   ```

## 🎨 自定义样式

可以通过修改CSS来自定义置顶标识的样式：

```css
/* 置顶标识样式 */
.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #000 !important;
}

/* 置顶按钮样式 */
.pin-btn .fa-thumbtack.text-warning {
    color: #ffc107 !important;
}
```

## 🚀 未来扩展

- 支持多级置顶（高、中、低优先级）
- 置顶时间限制（自动取消置顶）
- 置顶视频数量限制
- 置顶操作日志记录

## 📞 技术支持

如有问题，请查看：
1. 控制台错误信息
2. 数据库连接状态
3. API响应状态
4. 浏览器开发者工具

---

**注意**: 此功能需要管理员权限，普通用户无法操作置顶功能。
