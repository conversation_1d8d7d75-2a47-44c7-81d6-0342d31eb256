# 测试视频URL示例

## 用于测试的公开视频URL

### 示例视频1 - MP4格式
**视频URL:** `https://sample-videos.com/zip/10/mp4/SampleVideo_1280x720_1mb.mp4`
**缩略图URL:** `https://sample-videos.com/zip/10/jpg/SampleJPGImage_1280x720_1mb.jpg`
**标题:** 示例视频 - 720p测试
**描述:** 这是一个用于测试的示例视频，分辨率为1280x720
**格式:** mp4
**分辨率:** 720p

### 示例视频2 - WebM格式
**视频URL:** `https://file-examples.com/storage/fe86c86b8b66f8e0b8b7b3e/2017/10/file_example_WEBM_480_900KB.webm`
**缩略图URL:** `https://file-examples.com/storage/fe86c86b8b66f8e0b8b7b3e/2017/10/file_example_JPG_100kB.jpg`
**标题:** WebM格式测试视频
**描述:** 用于测试WebM格式播放的示例视频
**格式:** webm
**分辨率:** 480p

### 示例视频3 - 高清MP4
**视频URL:** `https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4`
**缩略图URL:** `https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/images/BigBuckBunny.jpg`
**标题:** Big Buck Bunny - 高清测试
**描述:** 经典的开源测试视频，高清画质
**格式:** mp4
**分辨率:** 1080p

## 使用说明

1. 打开添加视频页面：http://localhost:8080/admin/add
2. 复制上述任一视频URL到"视频URL地址"字段
3. 复制对应的缩略图URL到"缩略图URL地址"字段（可选）
4. 填写其他信息
5. 点击"保存视频"按钮

## 注意事项

- 这些是公开的测试视频URL，仅用于功能验证
- 实际使用时应该使用您自己的OSS存储的视频文件
- 确保URL可以正常访问且支持跨域请求
