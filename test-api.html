<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试 - 视频置顶功能</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        button { margin: 5px; padding: 8px 15px; }
        #results { background: #f5f5f5; padding: 10px; border-radius: 5px; height: 300px; overflow-y: auto; }
    </style>
</head>
<body>
    <h1>🎬 视频置顶功能 API 测试</h1>
    
    <div class="test-section">
        <h3>📋 测试视频列表</h3>
        <button onclick="loadVideos()">加载视频列表</button>
        <div id="videoList"></div>
    </div>
    
    <div class="test-section">
        <h3>📌 置顶功能测试</h3>
        <input type="number" id="videoId" placeholder="输入视频ID" value="1">
        <button onclick="togglePin()">切换置顶状态</button>
        <button onclick="setPin(true)">设置为置顶</button>
        <button onclick="setPin(false)">取消置顶</button>
    </div>
    
    <div class="test-section">
        <h3>📊 测试结果</h3>
        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:55680/api';
        
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const time = new Date().toLocaleTimeString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : 'info';
            results.innerHTML += `<div class="${className}">[${time}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }
        
        async function loadVideos() {
            try {
                log('正在加载视频列表...');
                const response = await fetch(`${API_BASE}/videos?size=10`);
                const result = await response.json();
                
                if (result.success) {
                    const videos = result.data;
                    let html = '<table border="1" style="width:100%; margin-top:10px;"><tr><th>ID</th><th>标题</th><th>置顶状态</th><th>创建时间</th></tr>';
                    
                    videos.forEach(video => {
                        const pinnedStatus = video.isPinned ? '✅ 已置顶' : '❌ 未置顶';
                        html += `
                            <tr>
                                <td>${video.id}</td>
                                <td>${video.title}</td>
                                <td>${pinnedStatus}</td>
                                <td>${new Date(video.createdTime).toLocaleString()}</td>
                            </tr>
                        `;
                    });
                    html += '</table>';
                    
                    document.getElementById('videoList').innerHTML = html;
                    log(`✅ 成功加载 ${videos.length} 个视频`, 'success');
                } else {
                    log(`❌ 加载失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        async function togglePin() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                log('请输入视频ID', 'error');
                return;
            }

            try {
                log(`正在切换视频 ${videoId} 的置顶状态...`);
                const response = await fetch(`${API_BASE}/videos/${videoId}/pin`, {
                    method: 'PUT'
                });

                log(`响应状态: ${response.status}`);

                const result = await response.json();
                log(`响应内容: ${JSON.stringify(result)}`);

                if (result.success) {
                    log(`✅ ${result.message}`, 'success');
                    loadVideos(); // 重新加载列表
                } else {
                    log(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        async function setPin(status) {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                log('请输入视频ID', 'error');
                return;
            }

            try {
                log(`正在设置视频 ${videoId} 置顶状态为: ${status}...`);
                const response = await fetch(`${API_BASE}/videos/${videoId}/pin/${status}`, {
                    method: 'PUT'
                });

                log(`响应状态: ${response.status}`);

                const result = await response.json();
                log(`响应内容: ${JSON.stringify(result)}`);

                if (result.success) {
                    log(`✅ ${result.message}`, 'success');
                    loadVideos(); // 重新加载列表
                } else {
                    log(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动加载视频列表
        window.onload = function() {
            log('🎬 视频置顶功能测试页面已加载');
            log('💡 应用运行在: http://localhost:54964');
            loadVideos();
        };
    </script>
</body>
</html>
