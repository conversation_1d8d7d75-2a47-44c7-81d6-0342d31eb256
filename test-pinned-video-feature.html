<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频置顶功能测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h1 class="mb-4">
            <i class="fas fa-thumbtack text-warning me-2"></i>
            视频置顶功能测试
        </h1>
        
        <div class="alert alert-info">
            <h5><i class="fas fa-info-circle me-2"></i>测试说明</h5>
            <p>此页面用于测试视频置顶功能的各项特性。请确保应用已启动并连接到数据库。</p>
        </div>

        <!-- 功能测试区域 -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-primary text-white">
                        <h5><i class="fas fa-cog me-2"></i>API测试</h5>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <label for="videoId" class="form-label">视频ID</label>
                            <input type="number" class="form-control" id="videoId" placeholder="输入视频ID">
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button class="btn btn-warning" onclick="togglePin()">
                                <i class="fas fa-thumbtack me-2"></i>切换置顶状态
                            </button>
                            <button class="btn btn-success" onclick="setPin(true)">
                                <i class="fas fa-arrow-up me-2"></i>设置为置顶
                            </button>
                            <button class="btn btn-secondary" onclick="setPin(false)">
                                <i class="fas fa-arrow-down me-2"></i>取消置顶
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header bg-success text-white">
                        <h5><i class="fas fa-list me-2"></i>视频列表</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-info mb-3" onclick="loadVideos()">
                            <i class="fas fa-refresh me-2"></i>加载视频列表
                        </button>
                        <div id="videoList">
                            <p class="text-muted">点击"加载视频列表"查看视频</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 测试结果 -->
        <div class="mt-4">
            <div class="card">
                <div class="card-header bg-dark text-white">
                    <h5><i class="fas fa-terminal me-2"></i>测试结果</h5>
                </div>
                <div class="card-body">
                    <div id="testResults" class="bg-light p-3 rounded" style="height: 200px; overflow-y: auto;">
                        <p class="text-muted">测试结果将显示在这里...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const API_BASE = 'http://localhost:8080/api';
        
        function log(message, type = 'info') {
            const results = document.getElementById('testResults');
            const time = new Date().toLocaleTimeString();
            const color = type === 'error' ? 'text-danger' : type === 'success' ? 'text-success' : 'text-info';
            results.innerHTML += `<div class="${color}">[${time}] ${message}</div>`;
            results.scrollTop = results.scrollHeight;
        }
        
        async function togglePin() {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                log('请输入视频ID', 'error');
                return;
            }
            
            try {
                log(`正在切换视频 ${videoId} 的置顶状态...`);
                const response = await fetch(`${API_BASE}/videos/${videoId}/pin`, {
                    method: 'PUT'
                });
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ ${result.message}`, 'success');
                } else {
                    log(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        async function setPin(status) {
            const videoId = document.getElementById('videoId').value;
            if (!videoId) {
                log('请输入视频ID', 'error');
                return;
            }
            
            try {
                log(`正在设置视频 ${videoId} 置顶状态为: ${status}...`);
                const response = await fetch(`${API_BASE}/videos/${videoId}/pin/${status}`, {
                    method: 'PUT'
                });
                const result = await response.json();
                
                if (result.success) {
                    log(`✅ ${result.message}`, 'success');
                } else {
                    log(`❌ ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        async function loadVideos() {
            try {
                log('正在加载视频列表...');
                const response = await fetch(`${API_BASE}/videos?size=10`);
                const result = await response.json();
                
                if (result.success) {
                    const videos = result.data;
                    let html = '';
                    
                    videos.forEach(video => {
                        const pinnedBadge = video.isPinned ? 
                            '<span class="badge bg-warning text-dark ms-2"><i class="fas fa-thumbtack me-1"></i>置顶</span>' : '';
                        
                        html += `
                            <div class="border rounded p-2 mb-2">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <strong>ID: ${video.id}</strong> - ${video.title}
                                        ${pinnedBadge}
                                    </div>
                                    <small class="text-muted">${new Date(video.createdTime).toLocaleString()}</small>
                                </div>
                            </div>
                        `;
                    });
                    
                    document.getElementById('videoList').innerHTML = html || '<p class="text-muted">暂无视频</p>';
                    log(`✅ 加载了 ${videos.length} 个视频`, 'success');
                } else {
                    log(`❌ 加载失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`❌ 请求失败: ${error.message}`, 'error');
            }
        }
        
        // 页面加载时自动加载视频列表
        window.onload = function() {
            log('🎬 视频置顶功能测试页面已加载');
            log('💡 提示: 请确保应用运行在 http://localhost:8080');
        };
    </script>
</body>
</html>
