<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试JSON格式</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">测试JSON格式和数据传输</h1>
        
        <div class="row">
            <div class="col-md-6">
                <h3>测试不同的JSON格式</h3>
                <div class="mb-3">
                    <label>视频ID:</label>
                    <input type="number" id="videoId" class="form-control" value="1">
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-danger me-2" onclick="testFormat1()">
                        格式1: {"isActive": false}
                    </button>
                    <button class="btn btn-warning me-2" onclick="testFormat2()">
                        格式2: {"isActive": "false"}
                    </button>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-success me-2" onclick="testFormat3()">
                        格式3: {"isActive": true}
                    </button>
                    <button class="btn btn-info me-2" onclick="testFormat4()">
                        格式4: {"isActive": "true"}
                    </button>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-secondary me-2" onclick="testFormat5()">
                        格式5: 完整对象更新
                    </button>
                </div>
                
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="getCurrentVideo()">
                        获取当前视频状态
                    </button>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>测试结果</h3>
                <div id="results" style="height: 500px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; background-color: #f8f9fa;">
                </div>
                <button class="btn btn-sm btn-outline-secondary mt-2" onclick="clearResults()">清空结果</button>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}"><strong>[${timestamp}]</strong> ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function getCurrentVideo() {
            const videoId = document.getElementById('videoId').value;
            log(`获取视频 ${videoId} 的当前状态...`);
            
            try {
                const response = await fetch(`/api/videos/${videoId}`);
                const result = await response.json();
                
                if (result.success) {
                    log(`当前状态: isActive = ${result.data.isActive} (类型: ${typeof result.data.isActive})`, 'success');
                    log(`完整数据: ${JSON.stringify(result.data, null, 2)}`);
                } else {
                    log(`获取失败: ${result.message}`, 'error');
                }
            } catch (error) {
                log(`网络错误: ${error.message}`, 'error');
            }
        }

        async function testFormat1() {
            const videoId = document.getElementById('videoId').value;
            const data = { isActive: false };
            await testUpdate(videoId, data, '布尔值 false');
        }

        async function testFormat2() {
            const videoId = document.getElementById('videoId').value;
            const data = { isActive: "false" };
            await testUpdate(videoId, data, '字符串 "false"');
        }

        async function testFormat3() {
            const videoId = document.getElementById('videoId').value;
            const data = { isActive: true };
            await testUpdate(videoId, data, '布尔值 true');
        }

        async function testFormat4() {
            const videoId = document.getElementById('videoId').value;
            const data = { isActive: "true" };
            await testUpdate(videoId, data, '字符串 "true"');
        }

        async function testFormat5() {
            const videoId = document.getElementById('videoId').value;
            
            // 先获取当前视频信息
            try {
                const getResponse = await fetch(`/api/videos/${videoId}`);
                const getResult = await getResponse.json();
                
                if (getResult.success) {
                    const currentVideo = getResult.data;
                    // 创建完整的更新对象，只修改isActive
                    const data = {
                        title: currentVideo.title,
                        description: currentVideo.description,
                        videoUrl: currentVideo.videoUrl,
                        thumbnailUrl: currentVideo.thumbnailUrl,
                        fileSize: currentVideo.fileSize,
                        videoFormat: currentVideo.videoFormat,
                        resolution: currentVideo.resolution,
                        isActive: false  // 设置为禁用
                    };
                    
                    await testUpdate(videoId, data, '完整对象更新 (isActive: false)');
                } else {
                    log(`无法获取视频信息: ${getResult.message}`, 'error');
                }
            } catch (error) {
                log(`获取视频信息失败: ${error.message}`, 'error');
            }
        }

        async function testUpdate(videoId, data, description) {
            log(`\n=== 测试 ${description} ===`);
            log(`发送数据: ${JSON.stringify(data, null, 2)}`);
            
            try {
                const response = await fetch(`/api/videos/${videoId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(data)
                });
                
                log(`响应状态: ${response.status} ${response.statusText}`);
                
                const result = await response.json();
                log(`响应数据: ${JSON.stringify(result, null, 2)}`);
                
                if (result.success) {
                    log(`更新成功！返回的isActive: ${result.data.isActive} (类型: ${typeof result.data.isActive})`, 'success');
                } else {
                    log(`更新失败: ${result.message}`, 'error');
                }
                
                // 验证更新结果
                setTimeout(async () => {
                    log('验证更新结果...');
                    await getCurrentVideo();
                }, 500);
                
            } catch (error) {
                log(`请求失败: ${error.message}`, 'error');
            }
        }

        // 页面加载时获取初始状态
        document.addEventListener('DOMContentLoaded', getCurrentVideo);
    </script>
</body>
</html>
