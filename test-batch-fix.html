<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试批量操作修复</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">测试批量操作修复</h1>
        
        <div class="alert alert-info">
            <h5>测试步骤：</h5>
            <ol>
                <li>点击"获取视频列表"加载视频</li>
                <li>选择一些视频（勾选复选框）</li>
                <li>点击"测试批量启用"或"测试批量禁用"</li>
                <li>观察API调用结果和状态变化</li>
            </ol>
        </div>

        <div class="row mb-4">
            <div class="col-md-6">
                <button class="btn btn-primary" onclick="loadVideos()">
                    <i class="fas fa-refresh me-1"></i>获取视频列表
                </button>
            </div>
            <div class="col-md-6">
                <div class="btn-group">
                    <button class="btn btn-success" onclick="testBatchEnable()" id="enableBtn" disabled>
                        <i class="fas fa-play me-1"></i>测试批量启用
                    </button>
                    <button class="btn btn-warning" onclick="testBatchDisable()" id="disableBtn" disabled>
                        <i class="fas fa-pause me-1"></i>测试批量禁用
                    </button>
                </div>
            </div>
        </div>

        <div id="videoList"></div>
        <div id="results" class="mt-4"></div>
    </div>

    <script>
        let selectedVideos = new Set();
        let allVideos = [];

        async function loadVideos() {
            try {
                showResult('正在加载视频列表...', 'info');
                
                const response = await fetch('/api/videos?page=0&size=20');
                const result = await response.json();
                
                if (result.success && result.data) {
                    allVideos = result.data;
                    displayVideos(result.data);
                    showResult(`成功加载 ${result.data.length} 个视频`, 'success');
                } else {
                    showResult('加载视频失败: ' + (result.message || '未知错误'), 'danger');
                }
            } catch (error) {
                showResult('加载视频失败: ' + error.message, 'danger');
            }
        }

        function displayVideos(videos) {
            const container = document.getElementById('videoList');
            
            container.innerHTML = `
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th width="50">
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                </th>
                                <th width="80">ID</th>
                                <th>标题</th>
                                <th width="100">当前状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${videos.map(video => `
                                <tr>
                                    <td>
                                        <input type="checkbox" value="${video.id}" onchange="updateSelection()">
                                    </td>
                                    <td><span class="badge bg-secondary">${video.id}</span></td>
                                    <td>${video.title}</td>
                                    <td>
                                        <span class="badge ${video.isActive ? 'bg-success' : 'bg-danger'}">
                                            ${video.isActive ? '启用' : '禁用'}
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
        }

        function toggleSelectAll() {
            const selectAll = document.getElementById('selectAll');
            const checkboxes = document.querySelectorAll('input[type="checkbox"][value]');
            
            checkboxes.forEach(cb => {
                cb.checked = selectAll.checked;
            });
            
            updateSelection();
        }

        function updateSelection() {
            const checkboxes = document.querySelectorAll('input[type="checkbox"][value]:checked');
            selectedVideos.clear();
            
            checkboxes.forEach(cb => {
                selectedVideos.add(parseInt(cb.value));
            });
            
            const hasSelection = selectedVideos.size > 0;
            document.getElementById('enableBtn').disabled = !hasSelection;
            document.getElementById('disableBtn').disabled = !hasSelection;
            
            showResult(`已选择 ${selectedVideos.size} 个视频: [${Array.from(selectedVideos).join(', ')}]`, 'info');
        }

        async function testBatchEnable() {
            if (selectedVideos.size === 0) {
                showResult('请先选择要启用的视频', 'warning');
                return;
            }

            showResult(`开始批量启用 ${selectedVideos.size} 个视频...`, 'info');
            let successCount = 0;
            let results = [];

            for (const videoId of selectedVideos) {
                try {
                    const response = await fetch(`/api/videos/${videoId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ isActive: true })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        successCount++;
                        results.push(`✅ 视频 ${videoId}: 启用成功`);
                    } else {
                        results.push(`❌ 视频 ${videoId}: ${result.message || '启用失败'}`);
                    }
                } catch (error) {
                    results.push(`❌ 视频 ${videoId}: 网络错误 - ${error.message}`);
                }
            }

            const resultText = `
                <h5>批量启用结果：</h5>
                <p>成功: ${successCount}/${selectedVideos.size}</p>
                <ul>
                    ${results.map(r => `<li>${r}</li>`).join('')}
                </ul>
            `;
            
            showResult(resultText, successCount === selectedVideos.size ? 'success' : 'warning');
            
            // 重新加载视频列表以查看状态变化
            setTimeout(loadVideos, 1000);
        }

        async function testBatchDisable() {
            if (selectedVideos.size === 0) {
                showResult('请先选择要禁用的视频', 'warning');
                return;
            }

            showResult(`开始批量禁用 ${selectedVideos.size} 个视频...`, 'info');
            let successCount = 0;
            let results = [];

            for (const videoId of selectedVideos) {
                try {
                    const response = await fetch(`/api/videos/${videoId}`, {
                        method: 'PUT',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ isActive: false })
                    });
                    
                    const result = await response.json();
                    
                    if (result.success) {
                        successCount++;
                        results.push(`✅ 视频 ${videoId}: 禁用成功`);
                    } else {
                        results.push(`❌ 视频 ${videoId}: ${result.message || '禁用失败'}`);
                    }
                } catch (error) {
                    results.push(`❌ 视频 ${videoId}: 网络错误 - ${error.message}`);
                }
            }

            const resultText = `
                <h5>批量禁用结果：</h5>
                <p>成功: ${successCount}/${selectedVideos.size}</p>
                <ul>
                    ${results.map(r => `<li>${r}</li>`).join('')}
                </ul>
            `;
            
            showResult(resultText, successCount === selectedVideos.size ? 'success' : 'warning');
            
            // 重新加载视频列表以查看状态变化
            setTimeout(loadVideos, 1000);
        }

        function showResult(message, type) {
            const container = document.getElementById('results');
            container.innerHTML = `
                <div class="alert alert-${type}">
                    ${message}
                </div>
            `;
        }

        // 页面加载时自动获取视频列表
        document.addEventListener('DOMContentLoaded', loadVideos);
    </script>
</body>
</html>
