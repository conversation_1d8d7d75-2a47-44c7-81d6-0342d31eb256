<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页跳转修复测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        /* 复制项目中的分页样式 */
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .pagination .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #667eea !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .pagination .page-item.disabled span.page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #667eea !important;
            cursor: not-allowed !important;
        }
        
        .fix-highlight {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #ffc107;
            margin: 15px 0;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .highlight {
            background: #4a5568;
            padding: 2px 4px;
            border-radius: 3px;
        }
        
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        
        .before, .after {
            padding: 15px;
            border-radius: 8px;
        }
        
        .before {
            background: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .after {
            background: #d4edda;
            border-left: 4px solid #28a745;
        }
        
        @media (max-width: 768px) {
            .before-after {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">分页跳转修复测试</h1>
        
        <!-- 问题描述 -->
        <div class="test-section">
            <h3 class="test-title">问题描述</h3>
            <div class="fix-highlight">
                <h5><i class="fas fa-exclamation-triangle"></i> 发现的问题</h5>
                <p>即使下一页没有视频，点击下一页按钮仍然会跳转到空白页面，这会导致用户体验不佳。</p>
            </div>
            
            <h5>问题原因分析：</h5>
            <ul>
                <li>前端模板中的条件判断可能不够严格</li>
                <li>hasNextPage变量的值可能没有正确传递</li>
                <li>Thymeleaf的条件表达式可能存在边界情况</li>
            </ul>
        </div>
        
        <!-- 修复方案 -->
        <div class="test-section">
            <h3 class="test-title">修复方案</h3>
            
            <div class="before-after">
                <div class="before">
                    <h5><i class="fas fa-times"></i> 修复前</h5>
                    <div class="code-block">
&lt;a th:if="<span class="highlight">${hasNextPage}</span>" class="page-link" 
   th:href="@{/videos(page=${currentPage + 1})}"&gt;
    &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
&lt;/a&gt;
&lt;span th:if="<span class="highlight">${!hasNextPage}</span>" class="page-link"&gt;
    &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
&lt;/span&gt;
                    </div>
                    <small class="text-muted">可能存在边界情况判断不准确</small>
                </div>
                
                <div class="after">
                    <h5><i class="fas fa-check"></i> 修复后</h5>
                    <div class="code-block">
&lt;a th:if="<span class="highlight">${hasNextPage == true}</span>" class="page-link" 
   th:href="@{/videos(page=${currentPage + 1})}"&gt;
    &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
&lt;/a&gt;
&lt;span th:if="<span class="highlight">${hasNextPage == false or hasNextPage == null}</span>" 
      class="page-link"&gt;
    &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
&lt;/span&gt;
                    </div>
                    <small class="text-muted">明确的布尔值比较，处理null情况</small>
                </div>
            </div>
        </div>
        
        <!-- 修复详情 -->
        <div class="test-section">
            <h3 class="test-title">修复详情</h3>
            
            <h5>1. 前端模板修复 (videos.html)</h5>
            <div class="code-block">
<span class="highlight">// 更严格的条件判断</span>
&lt;li class="page-item" th:classappend="${!hasNextPage ? 'disabled' : ''}"&gt;
    &lt;a th:if="<span class="highlight">${hasNextPage == true}</span>" class="page-link" 
       th:href="@{/videos(page=${currentPage + 1}, size=${pageSize}, keyword=${keyword})}"
       aria-label="下一页"&gt;
        &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
    &lt;/a&gt;
    &lt;span th:if="<span class="highlight">${hasNextPage == false or hasNextPage == null}</span>" 
          class="page-link" aria-label="下一页"&gt;
        &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
    &lt;/span&gt;
&lt;/li&gt;
            </div>
            
            <h5>2. 后端调试信息 (PageController.java)</h5>
            <div class="code-block">
<span class="highlight">// 添加调试日志</span>
List&lt;Video&gt; nextPageVideos = videoService.getAllVideos(page + 1, 1);
hasNextPage = !nextPageVideos.isEmpty();
System.out.println("当前页: " + page + 
                  ", 当前页视频数量: " + videos.size() + 
                  ", 下一页视频数量: " + nextPageVideos.size() + 
                  ", hasNextPage: " + hasNextPage);
            </div>
        </div>
        
        <!-- 测试场景 -->
        <div class="test-section">
            <h3 class="test-title">测试场景</h3>
            
            <!-- 场景1：正常情况 -->
            <div class="mb-4">
                <h5>场景1：有下一页（正常情况）</h5>
                <p>hasNextPage = true，下一页按钮应该可以点击</p>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="alert('上一页可点击'); return false;" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">2</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="alert('下一页可点击 - 正常跳转'); return false;" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
                <small class="text-success">✅ 预期行为：点击下一页会跳转到第3页</small>
            </div>
            
            <!-- 场景2：最后一页 -->
            <div class="mb-4">
                <h5>场景2：最后一页（修复重点）</h5>
                <p>hasNextPage = false，下一页按钮应该禁用且不可点击</p>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="alert('上一页可点击'); return false;" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">5</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页" onclick="alert('下一页已禁用 - 不应该跳转');">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <small class="text-danger">🚫 修复后行为：点击下一页不会有任何反应（不跳转）</small>
            </div>
            
            <!-- 场景3：只有一页 -->
            <div class="mb-4">
                <h5>场景3：只有一页</h5>
                <p>currentPage = 0 且 hasNextPage = false</p>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <small class="text-muted">ℹ️ 两个按钮都禁用，都不可点击</small>
            </div>
        </div>
        
        <!-- 验证步骤 -->
        <div class="test-section">
            <h3 class="test-title">验证步骤</h3>
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 如何验证修复效果</h5>
                <ol>
                    <li><strong>启动项目</strong>：运行 <code>mvn spring-boot:run</code></li>
                    <li><strong>访问视频页面</strong>：打开 <code>http://localhost:端口/videos</code></li>
                    <li><strong>导航到最后一页</strong>：
                        <ul>
                            <li>如果视频很少，可能第一页就是最后一页</li>
                            <li>如果视频较多，点击下一页直到到达最后一页</li>
                        </ul>
                    </li>
                    <li><strong>测试下一页按钮</strong>：
                        <ul>
                            <li>在最后一页，下一页按钮应该显示为禁用状态（白色背景）</li>
                            <li>点击下一页按钮应该没有任何反应，不会跳转</li>
                            <li>URL不应该改变</li>
                        </ul>
                    </li>
                    <li><strong>检查控制台日志</strong>：查看调试信息确认hasNextPage的值</li>
                </ol>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle"></i> 修复完成</h5>
                <p class="mb-0">
                    ✅ 前端模板条件判断更加严格<br>
                    ✅ 添加了调试日志便于排查问题<br>
                    ✅ 处理了hasNextPage为null的边界情况<br>
                    ✅ 确保禁用状态下完全不可点击
                </p>
            </div>
        </div>
        
        <!-- 相关文件 -->
        <div class="test-section">
            <h3 class="test-title">修改的文件</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>前端文件</h5>
                    <ul>
                        <li><code>videos.html</code> - 修复分页条件判断</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>后端文件</h5>
                    <ul>
                        <li><code>PageController.java</code> - 添加调试日志</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
