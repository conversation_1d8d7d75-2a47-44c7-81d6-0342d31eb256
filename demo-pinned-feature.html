<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>置顶功能演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .feature-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            background: #f8f9fa;
        }
        .status-demo {
            display: inline-flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
            margin: 0 10px;
        }
        .before-after {
            display: flex;
            justify-content: space-around;
            align-items: center;
            margin: 20px 0;
        }
        .arrow {
            font-size: 2rem;
            color: #007bff;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 class="text-center mb-4">
            <i class="fas fa-thumbtack text-warning me-2"></i>
            视频置顶功能演示
        </h1>

        <div class="feature-card">
            <h3><i class="fas fa-star text-warning me-2"></i>功能亮点</h3>
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success me-2"></i>置顶标识现在显示在状态栏下方</li>
                <li><i class="fas fa-check text-success me-2"></i>清晰的视觉层次，状态和置顶信息分离</li>
                <li><i class="fas fa-check text-success me-2"></i>响应式设计，适配各种屏幕尺寸</li>
                <li><i class="fas fa-check text-success me-2"></i>一键切换置顶状态</li>
            </ul>
        </div>

        <div class="feature-card">
            <h3><i class="fas fa-layout-alt me-2"></i>布局对比</h3>
            
            <div class="before-after">
                <div class="text-center">
                    <h5>修改前</h5>
                    <div class="status-demo">
                        <div class="d-flex align-items-center">
                            <strong>视频标题</strong>
                            <span class="badge bg-warning text-dark ms-2">
                                <i class="fas fa-thumbtack me-1"></i>置顶
                            </span>
                        </div>
                        <div class="mt-2">
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>启用
                            </span>
                        </div>
                    </div>
                    <small class="text-muted">置顶标识在标题旁边</small>
                </div>

                <div class="arrow">
                    <i class="fas fa-arrow-right"></i>
                </div>

                <div class="text-center">
                    <h5>修改后</h5>
                    <div class="status-demo">
                        <div>
                            <strong>视频标题</strong>
                        </div>
                        <div class="mt-2 d-flex flex-column align-items-center gap-1">
                            <span class="badge bg-success">
                                <i class="fas fa-check me-1"></i>启用
                            </span>
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-thumbtack me-1"></i>置顶
                            </span>
                        </div>
                    </div>
                    <small class="text-muted">置顶标识在状态下方</small>
                </div>
            </div>
        </div>

        <div class="feature-card">
            <h3><i class="fas fa-cogs me-2"></i>操作说明</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5><i class="fas fa-mouse-pointer me-2"></i>如何使用</h5>
                    <ol>
                        <li>访问管理页面：<code>http://localhost:54964/admin</code></li>
                        <li>找到要置顶的视频</li>
                        <li>点击操作列中的 📌 按钮</li>
                        <li>查看状态栏下方的置顶标识</li>
                    </ol>
                </div>
                <div class="col-md-6">
                    <h5><i class="fas fa-eye me-2"></i>视觉效果</h5>
                    <ul>
                        <li><strong>启用状态</strong>：绿色徽章 <span class="badge bg-success"><i class="fas fa-check"></i> 启用</span></li>
                        <li><strong>禁用状态</strong>：灰色徽章 <span class="badge bg-secondary"><i class="fas fa-pause"></i> 禁用</span></li>
                        <li><strong>置顶标识</strong>：黄色徽章 <span class="badge bg-warning text-dark"><i class="fas fa-thumbtack"></i> 置顶</span></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="feature-card">
            <h3><i class="fas fa-code me-2"></i>技术实现</h3>
            <div class="row">
                <div class="col-md-4">
                    <h6>HTML结构</h6>
                    <pre><code>&lt;td class="status-column"&gt;
  &lt;div class="d-flex flex-column"&gt;
    &lt;span class="badge bg-success"&gt;启用&lt;/span&gt;
    &lt;span class="badge bg-warning"&gt;置顶&lt;/span&gt;
  &lt;/div&gt;
&lt;/td&gt;</code></pre>
                </div>
                <div class="col-md-4">
                    <h6>CSS样式</h6>
                    <pre><code>.status-column .d-flex.flex-column {
  gap: 0.25rem;
}
.status-column .badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
}</code></pre>
                </div>
                <div class="col-md-4">
                    <h6>JavaScript更新</h6>
                    <pre><code>function updatePinBadge(videoId, isPinned) {
  const statusContainer = 
    document.querySelector('.status-column .d-flex');
  // 动态更新置顶标识
}</code></pre>
                </div>
            </div>
        </div>

        <div class="text-center mt-4">
            <a href="http://localhost:54964/admin" class="btn btn-primary btn-lg me-3">
                <i class="fas fa-cog me-2"></i>访问管理页面
            </a>
            <a href="file:///d:/admin/desktop/video-player-project/test-api.html" class="btn btn-success btn-lg">
                <i class="fas fa-flask me-2"></i>API测试页面
            </a>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
