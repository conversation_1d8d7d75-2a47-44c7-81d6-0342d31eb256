<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端分页测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .test-info {
            background: #e3f2fd;
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 8px;
            border-left: 4px solid #2196f3;
        }
        
        .device-info {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 1000;
            max-width: 200px;
        }
        
        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .video-card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin: 30px 0;
        }
        
        .page-btn {
            padding: 10px 15px;
            background: #2196f3;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
        }
        
        .page-btn:hover {
            background: #1976d2;
        }
        
        .page-btn.active {
            background: #ff6b6b;
        }
        
        .page-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        /* 手机端样式 */
        @media (max-width: 768px) {
            .video-grid {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .test-info {
                padding: 15px;
                font-size: 14px;
            }
            
            .device-info {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
                background: #333;
            }
        }
    </style>
</head>
<body>
    <div class="device-info" id="deviceInfo"></div>
    
    <div class="test-info">
        <h2>手机端分页测试</h2>
        <p><strong>功能说明：</strong></p>
        <ul>
            <li>桌面端：每页显示12个视频（默认）</li>
            <li>手机端：每页显示2个视频</li>
            <li>自动检测设备类型并调整分页</li>
        </ul>
        <p><strong>测试方法：</strong></p>
        <ol>
            <li>在桌面浏览器中访问 /videos 页面</li>
            <li>使用开发者工具切换到手机模拟器</li>
            <li>刷新页面，观察每页视频数量变化</li>
            <li>测试分页导航功能</li>
        </ol>
    </div>

    <h3>模拟视频列表（当前页面）</h3>
    <div class="video-grid" id="videoGrid">
        <!-- 视频卡片将通过JavaScript动态生成 -->
    </div>

    <div class="pagination" id="pagination">
        <!-- 分页按钮将通过JavaScript动态生成 -->
    </div>

    <script>
        // 模拟视频数据
        const allVideos = [
            { id: 1, title: "视频 1", description: "这是第一个视频" },
            { id: 2, title: "视频 2", description: "这是第二个视频" },
            { id: 3, title: "视频 3", description: "这是第三个视频" },
            { id: 4, title: "视频 4", description: "这是第四个视频" },
            { id: 5, title: "视频 5", description: "这是第五个视频" },
            { id: 6, title: "视频 6", description: "这是第六个视频" },
            { id: 7, title: "视频 7", description: "这是第七个视频" },
            { id: 8, title: "视频 8", description: "这是第八个视频" },
            { id: 9, title: "视频 9", description: "这是第九个视频" },
            { id: 10, title: "视频 10", description: "这是第十个视频" },
            { id: 11, title: "视频 11", description: "这是第十一个视频" },
            { id: 12, title: "视频 12", description: "这是第十二个视频" },
            { id: 13, title: "视频 13", description: "这是第十三个视频" },
            { id: 14, title: "视频 14", description: "这是第十四个视频" },
            { id: 15, title: "视频 15", description: "这是第十五个视频" }
        ];

        let currentPage = 0;
        let pageSize = 12;

        function isMobile() {
            return window.innerWidth <= 768;
        }

        function updatePageSize() {
            pageSize = isMobile() ? 2 : 12;
        }

        function updateDeviceInfo() {
            const width = window.innerWidth;
            const height = window.innerHeight;
            const deviceType = isMobile() ? '手机端' : '桌面端';
            const currentPageSize = isMobile() ? 2 : 12;
            
            document.getElementById('deviceInfo').innerHTML = `
                <div>屏幕: ${width}×${height}</div>
                <div>设备: ${deviceType}</div>
                <div>每页视频: ${currentPageSize}个</div>
                <div>当前页: ${currentPage + 1}</div>
            `;
        }

        function renderVideos() {
            const startIndex = currentPage * pageSize;
            const endIndex = startIndex + pageSize;
            const pageVideos = allVideos.slice(startIndex, endIndex);
            
            const videoGrid = document.getElementById('videoGrid');
            videoGrid.innerHTML = pageVideos.map(video => `
                <div class="video-card">
                    <h4>${video.title}</h4>
                    <p>${video.description}</p>
                    <small>视频ID: ${video.id}</small>
                </div>
            `).join('');
        }

        function renderPagination() {
            const totalPages = Math.ceil(allVideos.length / pageSize);
            const pagination = document.getElementById('pagination');
            
            let paginationHTML = '';
            
            // 上一页按钮
            paginationHTML += `
                <button class="page-btn" ${currentPage === 0 ? 'disabled' : ''} 
                        onclick="goToPage(${currentPage - 1})">
                    ← 上一页
                </button>
            `;
            
            // 当前页信息
            paginationHTML += `
                <span class="page-btn active">
                    ${currentPage + 1} / ${totalPages}
                </span>
            `;
            
            // 下一页按钮
            paginationHTML += `
                <button class="page-btn" ${currentPage >= totalPages - 1 ? 'disabled' : ''} 
                        onclick="goToPage(${currentPage + 1})">
                    下一页 →
                </button>
            `;
            
            pagination.innerHTML = paginationHTML;
        }

        function goToPage(page) {
            const totalPages = Math.ceil(allVideos.length / pageSize);
            if (page >= 0 && page < totalPages) {
                currentPage = page;
                renderVideos();
                renderPagination();
                updateDeviceInfo();
            }
        }

        function init() {
            updatePageSize();
            renderVideos();
            renderPagination();
            updateDeviceInfo();
        }

        // 监听窗口大小变化
        window.addEventListener('resize', function() {
            const oldPageSize = pageSize;
            updatePageSize();
            
            if (oldPageSize !== pageSize) {
                // 页面大小改变时，重置到第一页
                currentPage = 0;
                renderVideos();
                renderPagination();
            }
            updateDeviceInfo();
        });

        // 初始化
        init();
    </script>
</body>
</html>
