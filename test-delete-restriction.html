<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试删除限制功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">测试删除限制功能</h1>
        
        <div class="alert alert-success">
            <h5>✅ 新的删除限制规则：</h5>
            <ul class="mb-0">
                <li>只有被禁用状态（🈲）的视频才能被永久删除</li>
                <li>启用状态的视频不显示删除按钮</li>
                <li>批量删除时会自动过滤启用状态的视频</li>
                <li>后端API会验证视频状态，拒绝删除启用的视频</li>
            </ul>
        </div>

        <div class="row">
            <div class="col-md-6">
                <h3>删除流程</h3>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">正确的删除步骤</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li class="mb-2">
                                <strong>第一步：禁用视频</strong>
                                <br><small class="text-muted">点击视频行的"禁用"按钮，将视频状态改为禁用</small>
                            </li>
                            <li class="mb-2">
                                <strong>第二步：确认状态</strong>
                                <br><small class="text-muted">确认视频显示为禁用状态（灰色背景，🈲标识）</small>
                            </li>
                            <li class="mb-2">
                                <strong>第三步：删除视频</strong>
                                <br><small class="text-muted">此时删除按钮才会显示，点击进行永久删除</small>
                            </li>
                        </ol>
                        
                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>重要：</strong>永久删除操作不可撤销，请谨慎操作！
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <h3>测试验证</h3>
                
                <div class="mb-3">
                    <button class="btn btn-primary" onclick="testAdminPage()">
                        <i class="fas fa-external-link-alt me-1"></i>打开Admin页面测试
                    </button>
                    <button class="btn btn-secondary" onclick="testApiRestriction()">
                        <i class="fas fa-code me-1"></i>测试API限制
                    </button>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">测试结果</h5>
                    </div>
                    <div class="card-body">
                        <div id="testResults" style="height: 250px; overflow-y: auto; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                            <p class="text-muted">点击测试按钮查看结果</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <h3>功能对比</h3>
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead class="table-dark">
                            <tr>
                                <th>视频状态</th>
                                <th>删除按钮显示</th>
                                <th>可否删除</th>
                                <th>批量删除</th>
                                <th>说明</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="table-success">
                                <td>
                                    <span class="badge bg-success">
                                        <i class="fas fa-play me-1"></i>启用
                                    </span>
                                </td>
                                <td>❌ 不显示</td>
                                <td>❌ 不可删除</td>
                                <td>❌ 自动跳过</td>
                                <td>需要先禁用才能删除</td>
                            </tr>
                            <tr class="table-warning">
                                <td>
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-pause me-1"></i>禁用
                                    </span>
                                </td>
                                <td>✅ 显示</td>
                                <td>✅ 可删除</td>
                                <td>✅ 可批量删除</td>
                                <td>可以进行永久删除操作</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🔧 实现说明</h5>
                    </div>
                    <div class="card-body">
                        <h6>前端限制：</h6>
                        <ul>
                            <li><strong>按钮显示</strong>：使用Thymeleaf条件 <code>th:if="${!video.isActive}"</code> 只在禁用状态显示删除按钮</li>
                            <li><strong>JavaScript检查</strong>：删除前检查视频行的CSS类，确认为禁用状态</li>
                            <li><strong>批量删除</strong>：自动过滤启用状态的视频，只删除禁用的视频</li>
                        </ul>
                        
                        <h6>后端验证：</h6>
                        <ul>
                            <li><strong>状态检查</strong>：VideoService.permanentDeleteVideo() 方法检查 <code>video.getIsActive()</code></li>
                            <li><strong>异常处理</strong>：如果视频为启用状态，抛出 <code>IllegalStateException</code></li>
                            <li><strong>API响应</strong>：返回400错误和明确的错误信息</li>
                        </ul>
                        
                        <h6>用户体验：</h6>
                        <ul>
                            <li><strong>清晰提示</strong>：明确告知用户只能删除禁用状态的视频</li>
                            <li><strong>防误操作</strong>：多层验证防止意外删除重要视频</li>
                            <li><strong>操作引导</strong>：提示用户先禁用再删除的正确流程</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('testResults');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = {
                'info': 'text-primary',
                'success': 'text-success',
                'error': 'text-danger',
                'warning': 'text-warning'
            }[type] || 'text-dark';
            
            resultsDiv.innerHTML += `<div class="${colorClass}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function testAdminPage() {
            log('打开Admin页面进行测试...', 'info');
            log('请按以下步骤测试：', 'info');
            log('1. 查看启用状态的视频是否没有删除按钮', 'info');
            log('2. 禁用一个视频，确认删除按钮出现', 'info');
            log('3. 尝试删除禁用状态的视频', 'info');
            log('4. 测试批量删除功能', 'info');
            window.open('/admin', '_blank');
        }

        async function testApiRestriction() {
            log('开始测试API删除限制...', 'info');
            
            try {
                // 首先获取视频列表
                const response = await fetch('/api/videos?page=0&size=10');
                const result = await response.json();
                
                if (result.success && result.data && result.data.length > 0) {
                    log(`获取到 ${result.data.length} 个视频`, 'success');
                    
                    // 找到一个启用状态的视频进行测试
                    const activeVideo = result.data.find(video => video.isActive);
                    
                    if (activeVideo) {
                        log(`找到启用状态的视频: ${activeVideo.title} (ID: ${activeVideo.id})`, 'info');
                        log('尝试删除启用状态的视频...', 'warning');
                        
                        // 尝试删除启用状态的视频
                        const deleteResponse = await fetch(`/api/videos/${activeVideo.id}`, {
                            method: 'DELETE'
                        });
                        
                        const deleteResult = await deleteResponse.json();
                        
                        if (deleteResponse.status === 400 && !deleteResult.success) {
                            log('✅ API正确拒绝删除启用状态的视频', 'success');
                            log(`错误信息: ${deleteResult.message}`, 'info');
                        } else {
                            log('❌ API未正确限制删除操作', 'error');
                        }
                    } else {
                        log('⚠️ 没有找到启用状态的视频进行测试', 'warning');
                    }
                    
                    // 查找禁用状态的视频
                    const disabledVideo = result.data.find(video => !video.isActive);
                    if (disabledVideo) {
                        log(`找到禁用状态的视频: ${disabledVideo.title} (ID: ${disabledVideo.id})`, 'info');
                        log('禁用状态的视频应该可以删除（但我们不会真的删除）', 'info');
                    } else {
                        log('没有找到禁用状态的视频', 'info');
                    }
                    
                } else {
                    log('❌ 无法获取视频列表进行测试', 'error');
                }
                
            } catch (error) {
                log(`❌ 测试过程中发生错误: ${error.message}`, 'error');
            }
            
            log('API限制测试完成', 'info');
        }

        // 页面加载时显示说明
        document.addEventListener('DOMContentLoaded', function() {
            log('删除限制功能测试页面加载完成', 'info');
            log('现在只有禁用状态的视频才能被永久删除', 'success');
            log('可以开始测试新的删除限制功能', 'info');
        });
    </script>
</body>
</html>
