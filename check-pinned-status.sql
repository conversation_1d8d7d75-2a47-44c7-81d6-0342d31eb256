-- 检查视频置顶状态的SQL脚本
USE video_player;

-- 显示所有视频的置顶状态
SELECT 
    id,
    title,
    is_active,
    is_pinned,
    created_time,
    updated_time
FROM videos 
ORDER BY is_pinned DESC, created_time DESC;

-- 统计置顶视频数量
SELECT 
    COUNT(*) as total_videos,
    SUM(CASE WHEN is_pinned = 1 THEN 1 ELSE 0 END) as pinned_videos,
    SUM(CASE WHEN is_pinned = 0 OR is_pinned IS NULL THEN 1 ELSE 0 END) as unpinned_videos
FROM videos;

-- 显示置顶视频详情
SELECT 
    id,
    title,
    is_active,
    is_pinned,
    created_time
FROM videos 
WHERE is_pinned = 1
ORDER BY created_time DESC;
