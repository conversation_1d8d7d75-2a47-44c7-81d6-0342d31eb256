<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分页逻辑测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-section {
            background: white;
            padding: 30px;
            margin: 20px 0;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }
        
        /* 复制项目中的分页样式 */
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #667eea;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .pagination .page-item.active .page-link {
            background: #667eea;
            border-color: #667eea;
            color: white;
        }

        .pagination .page-item.disabled .page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #667eea !important;
            cursor: not-allowed !important;
            pointer-events: none !important;
        }

        .pagination .page-item.disabled span.page-link {
            background: #ffffff !important;
            border-color: #dee2e6 !important;
            color: #667eea !important;
            cursor: not-allowed !important;
        }
        
        .logic-demo {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #667eea;
        }
        
        .status-indicator {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status-enabled {
            background: #d4edda;
            color: #155724;
        }
        
        .status-disabled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .highlight {
            background: #4a5568;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="text-center mb-4">分页逻辑测试 - 下一页没有视频时禁用</h1>
        
        <!-- 功能说明 -->
        <div class="test-section">
            <h3 class="test-title">功能说明</h3>
            <p>项目已经实现了正确的分页逻辑，当下一页没有视频时会自动禁用下一页按钮：</p>
            
            <div class="logic-demo">
                <h5><i class="fas fa-cog"></i> 后端逻辑</h5>
                <ul>
                    <li>查询当前页的视频数据</li>
                    <li>尝试查询下一页的第一个视频</li>
                    <li>如果下一页没有视频，设置 <code>hasNextPage = false</code></li>
                    <li>将 <code>hasNextPage</code> 传递给前端模板</li>
                </ul>
            </div>
            
            <div class="logic-demo">
                <h5><i class="fas fa-code"></i> 前端逻辑</h5>
                <ul>
                    <li>根据 <code>hasNextPage</code> 变量控制下一页按钮状态</li>
                    <li>如果 <code>hasNextPage = false</code>，添加 <code>disabled</code> 类</li>
                    <li>禁用状态下显示 <code>&lt;span&gt;</code> 而不是 <code>&lt;a&gt;</code></li>
                    <li>应用禁用样式（白色背景，不可点击）</li>
                </ul>
            </div>
        </div>
        
        <!-- 代码实现 -->
        <div class="test-section">
            <h3 class="test-title">代码实现</h3>
            
            <h5>1. 后端控制器逻辑 (PageController.java)</h5>
            <div class="code-block">
<span class="highlight">// 检查是否有下一页：尝试获取下一页的第一个视频</span>
List&lt;Video&gt; nextPageVideos = videoService.getAllVideos(page + 1, 1);
boolean hasNextPage = !nextPageVideos.isEmpty();

<span class="highlight">// 传递给模板</span>
model.addAttribute("hasNextPage", hasNextPage);
            </div>
            
            <h5>2. 前端模板逻辑 (videos.html)</h5>
            <div class="code-block">
&lt;li class="page-item" <span class="highlight">th:classappend="${!hasNextPage ? 'disabled' : ''}"</span>&gt;
    &lt;a <span class="highlight">th:if="${hasNextPage}"</span> class="page-link" 
       th:href="@{/videos(page=${currentPage + 1}, size=${pageSize}, keyword=${keyword})}"
       aria-label="下一页"&gt;
        &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
    &lt;/a&gt;
    &lt;span <span class="highlight">th:if="${!hasNextPage}"</span> class="page-link" aria-label="下一页"&gt;
        &lt;i class="fas fa-chevron-right"&gt;&lt;/i&gt;
    &lt;/span&gt;
&lt;/li&gt;
            </div>
        </div>
        
        <!-- 场景演示 -->
        <div class="test-section">
            <h3 class="test-title">场景演示</h3>
            
            <!-- 场景1：有下一页 -->
            <div class="mb-4">
                <h5>场景1：有下一页 <span class="status-indicator status-enabled">下一页可用</span></h5>
                <p>当前页：第2页，总共5页，下一页有视频</p>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">2</span>
                        </li>
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </a>
                        </li>
                    </ul>
                </nav>
                <small class="text-muted">hasNextPage = true，下一页按钮可点击</small>
            </div>
            
            <!-- 场景2：没有下一页 -->
            <div class="mb-4">
                <h5>场景2：没有下一页 <span class="status-indicator status-disabled">下一页禁用</span></h5>
                <p>当前页：第5页（最后一页），下一页没有视频</p>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item">
                            <a class="page-link" href="#" onclick="return false;" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </a>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">5</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <small class="text-muted">hasNextPage = false，下一页按钮禁用且不可点击</small>
            </div>
            
            <!-- 场景3：只有一页 -->
            <div class="mb-4">
                <h5>场景3：只有一页 <span class="status-indicator status-disabled">两个按钮都禁用</span></h5>
                <p>总共只有1页视频，上一页和下一页都没有内容</p>
                <nav class="d-flex justify-content-center">
                    <ul class="pagination">
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="上一页">
                                <i class="fas fa-chevron-left"></i>
                            </span>
                        </li>
                        <li class="page-item active">
                            <span class="page-link">1</span>
                        </li>
                        <li class="page-item disabled">
                            <span class="page-link" aria-label="下一页">
                                <i class="fas fa-chevron-right"></i>
                            </span>
                        </li>
                    </ul>
                </nav>
                <small class="text-muted">currentPage = 0 且 hasNextPage = false</small>
            </div>
        </div>
        
        <!-- 测试建议 -->
        <div class="test-section">
            <h3 class="test-title">测试建议</h3>
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle"></i> 如何验证功能</h5>
                <ol>
                    <li><strong>启动项目</strong>：运行 <code>mvn spring-boot:run</code></li>
                    <li><strong>访问视频页面</strong>：打开 <code>http://localhost:端口/videos</code></li>
                    <li><strong>测试分页</strong>：
                        <ul>
                            <li>如果视频总数 ≤ 每页显示数量，下一页按钮应该禁用</li>
                            <li>导航到最后一页，下一页按钮应该禁用</li>
                            <li>在中间页面，下一页按钮应该可用</li>
                        </ul>
                    </li>
                    <li><strong>手机端测试</strong>：每页只显示2个视频，更容易测试分页</li>
                </ol>
            </div>
            
            <div class="alert alert-success">
                <h5><i class="fas fa-check-circle"></i> 功能已实现</h5>
                <p class="mb-0">
                    ✅ 后端已正确实现 <code>hasNextPage</code> 逻辑<br>
                    ✅ 前端已正确处理禁用状态<br>
                    ✅ CSS样式已统一设置<br>
                    ✅ 手机端分页已优化（每页2个视频）
                </p>
            </div>
        </div>
        
        <!-- 相关文件 -->
        <div class="test-section">
            <h3 class="test-title">相关文件</h3>
            <div class="row">
                <div class="col-md-6">
                    <h5>后端文件</h5>
                    <ul>
                        <li><code>PageController.java</code> - 分页逻辑控制</li>
                        <li><code>VideoService.java</code> - 视频查询服务</li>
                        <li><code>VideoRepository.java</code> - 数据访问层</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h5>前端文件</h5>
                    <ul>
                        <li><code>videos.html</code> - 视频列表模板</li>
                        <li><code>search-results.html</code> - 搜索结果模板</li>
                        <li><code>videos-style.css</code> - 分页样式</li>
                        <li><code>style.css</code> - 全局分页样式</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
