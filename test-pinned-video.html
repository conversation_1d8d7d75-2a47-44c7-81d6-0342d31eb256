<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试置顶视频功能</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .video-card {
            transition: all 0.3s ease;
            border: none;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 15px rgba(0,0,0,0.1);
            position: relative;
        }

        .video-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 30px rgba(0,0,0,0.15);
        }

        /* 置顶视频样式 */
        .pinned-video {
            border: 2px solid #ff6b6b !important;
            box-shadow: 0 4px 20px rgba(255, 107, 107, 0.3) !important;
        }

        .pinned-video:hover {
            box-shadow: 0 8px 35px rgba(255, 107, 107, 0.4) !important;
        }

        /* 置顶标识 */
        .pinned-badge {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #ff6b6b, #ff5252);
            color: white;
            padding: 4px 8px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 600;
            z-index: 10;
            display: flex;
            align-items: center;
            gap: 4px;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.4);
            animation: pinBounce 2s ease-in-out infinite;
        }

        .pinned-badge i {
            font-size: 10px;
            transform: rotate(45deg);
        }

        /* 置顶标识动画 */
        @keyframes pinBounce {
            0%, 100% {
                transform: translateY(0);
            }
            50% {
                transform: translateY(-2px);
            }
        }

        .video-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
            gap: 1.5rem;
        }
    </style>
</head>
<body>
    <div class="container my-5">
        <h1 class="text-center mb-4">测试置顶视频功能</h1>
        
        <div class="row mb-4">
            <div class="col-md-6">
                <button class="btn btn-primary" onclick="loadVideos()">
                    <i class="fas fa-refresh me-1"></i>加载视频列表
                </button>
            </div>
            <div class="col-md-6">
                <div class="input-group">
                    <input type="text" class="form-control" id="searchInput" placeholder="搜索视频...">
                    <button class="btn btn-outline-secondary" onclick="searchVideos()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </div>
        </div>

        <div id="videoContainer" class="video-grid">
            <!-- 视频卡片将在这里动态加载 -->
        </div>

        <div id="loading" class="text-center mt-4" style="display: none;">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    </div>

    <script>
        async function loadVideos() {
            const container = document.getElementById('videoContainer');
            const loading = document.getElementById('loading');
            
            loading.style.display = 'block';
            container.innerHTML = '';
            
            try {
                const response = await fetch('/api/videos?page=0&size=12');
                const result = await response.json();
                
                if (result.success && result.data) {
                    displayVideos(result.data);
                } else {
                    container.innerHTML = '<div class="alert alert-warning">没有找到视频</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="alert alert-danger">加载失败: ' + error.message + '</div>';
            } finally {
                loading.style.display = 'none';
            }
        }

        async function searchVideos() {
            const keyword = document.getElementById('searchInput').value.trim();
            if (!keyword) {
                loadVideos();
                return;
            }

            const container = document.getElementById('videoContainer');
            const loading = document.getElementById('loading');
            
            loading.style.display = 'block';
            container.innerHTML = '';
            
            try {
                const response = await fetch(`/api/videos/search?keyword=${encodeURIComponent(keyword)}&page=0&size=12`);
                const result = await response.json();
                
                if (result.success && result.data) {
                    displayVideos(result.data);
                } else {
                    container.innerHTML = '<div class="alert alert-warning">没有找到相关视频</div>';
                }
            } catch (error) {
                container.innerHTML = '<div class="alert alert-danger">搜索失败: ' + error.message + '</div>';
            } finally {
                loading.style.display = 'none';
            }
        }

        function displayVideos(videos) {
            const container = document.getElementById('videoContainer');
            
            if (!videos || videos.length === 0) {
                container.innerHTML = '<div class="alert alert-info">暂无视频</div>';
                return;
            }

            container.innerHTML = videos.map(video => `
                <article class="video-card card h-100 shadow-sm ${video.id == 1 ? 'pinned-video' : ''}" 
                         style="${video.id == 1 ? 'order: -1;' : ''}">
                    ${video.id == 1 ? `
                        <div class="pinned-badge">
                            <i class="fas fa-thumbtack"></i>
                            <span>置顶</span>
                        </div>
                    ` : ''}
                    
                    <a href="/play/${video.id}" class="video-thumbnail text-decoration-none">
                        <img src="${video.thumbnailUrl || '/images/default-thumbnail.jpg'}"
                             class="card-img-top"
                             alt="${video.title}"
                             style="height: 200px; object-fit: cover;"
                             onerror="this.src='/images/default-thumbnail.jpg'">
                    </a>
                    <div class="card-body">
                        <h3 class="card-title h5">${video.title}</h3>
                        <div class="video-stats">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                ${new Date(video.createdTime).toLocaleDateString('zh-CN')}
                            </small>
                            <br>
                            <small class="text-muted">ID: ${video.id}</small>
                        </div>
                    </div>
                </article>
            `).join('');
        }

        // 页面加载时自动加载视频
        document.addEventListener('DOMContentLoaded', loadVideos);

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchVideos();
            }
        });
    </script>
</body>
</html>
