<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ID左对齐演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .id-container {
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .id-collapsed {
            max-width: 50px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }

        .id-expanded {
            max-width: none;
            word-break: break-all;
        }

        .id-toggle-icon {
            font-size: 0.8em;
            margin-left: 4px;
            transition: transform 0.3s ease;
        }

        .id-expanded .id-toggle-icon {
            transform: rotate(180deg);
        }

        .id-container:hover {
            background-color: #e9ecef !important;
            transform: scale(1.02);
        }

        .id-full-text {
            font-family: 'Courier New', monospace;
            font-size: 0.85em;
            letter-spacing: 0.5px;
        }

        .comparison-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row">
            <div class="col-12">
                <h1 class="text-center mb-4">📐 ID左对齐演示</h1>
                <p class="text-center text-muted">展示table-light sticky-top表头中ID列的左对齐效果</p>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-md-10 mx-auto">
                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">📊 对齐方式对比</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6 class="text-center mb-3">居中对齐 (旧版)</h6>
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="text-center" style="width: 80px;">ID</th>
                                            <th>标题</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="text-center">
                                                <span class="badge bg-light text-dark">tJPe...</span>
                                            </td>
                                            <td>春季养生指南</td>
                                        </tr>
                                        <tr>
                                            <td class="text-center">
                                                <span class="badge bg-light text-dark">ahfa...</span>
                                            </td>
                                            <td>健康饮食搭配</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-md-6">
                                <h6 class="text-center mb-3">左对齐 (新版)</h6>
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th class="text-start" style="width: 80px;">ID</th>
                                            <th>标题</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td class="text-start">
                                                <span class="badge bg-light text-dark">tJPe...</span>
                                            </td>
                                            <td>春季养生指南</td>
                                        </tr>
                                        <tr>
                                            <td class="text-start">
                                                <span class="badge bg-light text-dark">ahfa...</span>
                                            </td>
                                            <td>健康饮食搭配</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mb-4">
            <div class="col-12">
                <div class="card comparison-table">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">📋 实际效果演示 (table-light sticky-top)</h5>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light sticky-top">
                                    <tr>
                                        <th style="width: 1px;" class="text-center">
                                            <input type="checkbox" class="form-check-input">
                                        </th>
                                        <th style="width: 10px;padding: 8px 0px;" class="text-start">ID</th>
                                        <th style="width: 100px;padding: 8px 0px;" class="text-center">缩略图</th>
                                        <th>详情</th>
                                        <th style="width: 10px;padding: 8px 0px;" class="text-center">状态</th>
                                        <th style="width: 10px;padding: 8px 0px;" class="text-center">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td class="text-center">
                                            <input type="checkbox" class="form-check-input">
                                        </td>
                                        <td class="text-start" style="padding: 8px 0px;">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                                                 data-full-id="tJPesWXD2wmwL2Qn7o"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('tJPesWXD2wmwL2Qn7o')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">tJPe...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">tJPesWXD2wmwL2Qn7o</div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <img src="https://via.placeholder.com/60x40/007bff/ffffff?text=缩略图" 
                                                 class="img-thumbnail" style="width: 60px; height: 40px;">
                                        </td>
                                        <td>
                                            <div>
                                                <strong>春季养生指南</strong><br>
                                                <small class="text-muted">详细介绍春季养生的各种方法和注意事项</small>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-success">启用</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">
                                            <input type="checkbox" class="form-check-input">
                                        </td>
                                        <td class="text-start" style="padding: 8px 0px;">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                                                 data-full-id="ahfa2nja66cDhl8Aco"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('ahfa2nja66cDhl8Aco')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">ahfa...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">ahfa2nja66cDhl8Aco</div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <img src="https://via.placeholder.com/60x40/28a745/ffffff?text=缩略图" 
                                                 class="img-thumbnail" style="width: 60px; height: 40px;">
                                        </td>
                                        <td>
                                            <div>
                                                <strong>健康饮食搭配</strong><br>
                                                <small class="text-muted">科学的饮食搭配方案，营养均衡更健康</small>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-success">启用</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-warning"><i class="fas fa-edit"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td class="text-center">
                                            <input type="checkbox" class="form-check-input">
                                        </td>
                                        <td class="text-start" style="padding: 8px 0px;">
                                            <div class="id-container badge bg-light text-dark fw-bold id-collapsed" 
                                                 data-full-id="Hjn4J0RkPPFavRrhcz"
                                                 onclick="toggleIdDisplay(this)"
                                                 ondblclick="copyVideoId('Hjn4J0RkPPFavRrhcz')"
                                                 title="单击展开/折叠，双击复制ID">
                                                <span class="id-display-text">Hjn4...</span>
                                                <i class="fas fa-chevron-down id-toggle-icon"></i>
                                                <div class="id-full-text" style="display: none;">Hjn4J0RkPPFavRrhcz</div>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <img src="https://via.placeholder.com/60x40/6c757d/ffffff?text=缩略图" 
                                                 class="img-thumbnail" style="width: 60px; height: 40px;">
                                        </td>
                                        <td>
                                            <div>
                                                <strong>运动健身教程</strong><br>
                                                <small class="text-muted">从基础到进阶的完整健身指导</small>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-secondary">禁用</span>
                                        </td>
                                        <td class="text-center">
                                            <div class="btn-group" role="group">
                                                <button class="btn btn-sm btn-outline-primary"><i class="fas fa-play"></i></button>
                                                <button class="btn btn-sm btn-outline-danger"><i class="fas fa-trash"></i></button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">✨ 左对齐优势</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-align-left text-primary me-2"></i>
                                <strong>视觉一致性：</strong>与文本内容对齐方式统一
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-eye text-success me-2"></i>
                                <strong>阅读习惯：</strong>符合从左到右的阅读习惯
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-expand-arrows-alt text-info me-2"></i>
                                <strong>空间利用：</strong>更好地利用列宽空间
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-mobile-alt text-warning me-2"></i>
                                <strong>移动友好：</strong>在窄屏幕上表现更佳
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">🎯 设计原则</h5>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-balance-scale text-primary me-2"></i>
                                <strong>对齐一致：</strong>表头和数据行保持一致
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-users text-success me-2"></i>
                                <strong>用户体验：</strong>减少视觉跳跃，提升可读性
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-cogs text-info me-2"></i>
                                <strong>功能优先：</strong>ID作为标识符，左对齐更实用
                            </li>
                            <li class="mb-2">
                                <i class="fas fa-palette text-warning me-2"></i>
                                <strong>界面美观：</strong>整体布局更加协调
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>说明：</strong>ID列现在使用左对齐 (text-start)，与表格的整体设计风格更加协调，提供更好的用户体验。
                </div>
            </div>
        </div>
    </div>

    <script>
        function toggleIdDisplay(element) {
            const isExpanded = element.classList.contains('id-expanded');
            const fullId = element.dataset.fullId;
            const displayText = element.querySelector('.id-display-text');
            const fullText = element.querySelector('.id-full-text');
            const icon = element.querySelector('.id-toggle-icon');
            
            if (isExpanded) {
                element.classList.remove('id-expanded');
                element.classList.add('id-collapsed');
                displayText.textContent = fullId.substring(0, 4) + '...';
                displayText.style.display = 'inline';
                fullText.style.display = 'none';
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                element.classList.remove('id-collapsed');
                element.classList.add('id-expanded');
                displayText.style.display = 'none';
                fullText.style.display = 'block';
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        }

        function copyVideoId(videoId) {
            if (navigator.clipboard) {
                navigator.clipboard.writeText(videoId).then(() => {
                    showAlert('视频ID已复制: ' + videoId, 'success');
                });
            } else {
                const textArea = document.createElement('textarea');
                textArea.value = videoId;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                showAlert('视频ID已复制: ' + videoId, 'success');
            }
        }

        function showAlert(message, type) {
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type === 'success' ? 'success' : 'danger'} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `${message}<button type="button" class="btn-close" data-bs-dismiss="alert"></button>`;
            document.body.appendChild(alertDiv);
            setTimeout(() => alertDiv.remove(), 3000);
        }
    </script>
</body>
</html>
