package com.videoplayer.repository;

import com.videoplayer.entity.Video;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 视频数据访问接口
 */
@Repository
public interface VideoRepository extends JpaRepository<Video, Long> {

    // 基础查询
    Page<Video> findByIsActiveTrue(Pageable pageable);
    List<Video> findByIsActiveTrue();
    Optional<Video> findByIdAndIsActiveTrue(Long id);
    long countByIsActiveTrue();

    // 搜索查询
    List<Video> findByTitleContainingIgnoreCase(String title);

    @Query("SELECT v FROM Video v WHERE v.isActive = true AND " +
           "(LOWER(v.title) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(v.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    Page<Video> searchVideos(@Param("keyword") String keyword, Pageable pageable);

    // 原生SQL强制删除方法
    @Modifying
    @Query(value = "DELETE FROM videos WHERE id = :id", nativeQuery = true)
    int deleteVideoById(@Param("id") Long id);
}

